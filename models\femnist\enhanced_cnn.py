import tensorflow as tf

from model import Model
import numpy as np


IMAGE_SIZE = 28


class ClientModel(Model):
    def __init__(self, seed, lr, num_classes, optimizer_type='sgd', dropout_rate=0.5, use_batch_norm=True):
        """
        Enhanced CNN model for FEMNIST with additional options for training.
        
        Args:
            seed: Random seed for reproducibility
            lr: Learning rate
            num_classes: Number of output classes
            optimizer_type: Type of optimizer ('sgd', 'adam', 'momentum')
            dropout_rate: Dropout rate (0.0 means no dropout)
            use_batch_norm: Whether to use batch normalization
        """
        self.num_classes = num_classes
        self.optimizer_type = optimizer_type
        self.dropout_rate = dropout_rate
        self.use_batch_norm = use_batch_norm
        super(ClientModel, self).__init__(seed, lr)

    def create_optimizer(self):
        """Create the optimizer based on the specified type."""
        if self.optimizer_type == 'adam':
            return tf.train.AdamOptimizer(learning_rate=self.lr)
        elif self.optimizer_type == 'momentum':
            return tf.train.MomentumOptimizer(learning_rate=self.lr, momentum=0.9)
        else:  # Default to SGD
            return tf.train.GradientDescentOptimizer(learning_rate=self.lr)

    def create_model(self):
        """Enhanced model function for CNN with batch norm and dropout."""
        # Set up placeholders
        features = tf.placeholder(
            tf.float32, shape=[None, IMAGE_SIZE * IMAGE_SIZE], name='features')
        labels = tf.placeholder(tf.int64, shape=[None], name='labels')
        training = tf.placeholder_with_default(False, shape=[], name='training')
        
        # Reshape features for convolution
        input_layer = tf.reshape(features, [-1, IMAGE_SIZE, IMAGE_SIZE, 1])
        
        # First convolutional block
        conv1 = tf.layers.conv2d(
            inputs=input_layer,
            filters=32,
            kernel_size=[5, 5],
            padding="same",
            activation=None)  # No activation before batch norm
            
        if self.use_batch_norm:
            conv1 = tf.layers.batch_normalization(conv1, training=training)
            
        # Apply ReLU activation after batch norm
        conv1 = tf.nn.relu(conv1)
        
        # Max pooling
        pool1 = tf.layers.max_pooling2d(inputs=conv1, pool_size=[2, 2], strides=2)
        
        # Second convolutional block
        conv2 = tf.layers.conv2d(
            inputs=pool1,
            filters=64,
            kernel_size=[5, 5],
            padding="same",
            activation=None)  # No activation before batch norm
            
        if self.use_batch_norm:
            conv2 = tf.layers.batch_normalization(conv2, training=training)
            
        # Apply ReLU activation after batch norm
        conv2 = tf.nn.relu(conv2)
        
        # Max pooling
        pool2 = tf.layers.max_pooling2d(inputs=conv2, pool_size=[2, 2], strides=2)
        
        # Flatten
        pool2_flat = tf.reshape(pool2, [-1, 7 * 7 * 64])
        
        # Dense layer
        dense = tf.layers.dense(inputs=pool2_flat, units=2048, activation=None)
        
        if self.use_batch_norm:
            dense = tf.layers.batch_normalization(dense, training=training)
        
        dense = tf.nn.relu(dense)
        
        # Apply dropout
        if self.dropout_rate > 0:
            dense = tf.layers.dropout(
                inputs=dense, 
                rate=self.dropout_rate, 
                training=training)
        
        # Output layer
        logits = tf.layers.dense(inputs=dense, units=self.num_classes)
        
        # Predictions
        predictions = {
            "classes": tf.argmax(input=logits, axis=1),
            "probabilities": tf.nn.softmax(logits, name="softmax_tensor")
        }
        
        # Loss
        loss = tf.losses.sparse_softmax_cross_entropy(labels=labels, logits=logits)
        
        # Update batch norm moving averages
        update_ops = tf.get_collection(tf.GraphKeys.UPDATE_OPS)
        with tf.control_dependencies(update_ops):
            # Create optimizer
            self.optimizer = self.create_optimizer()
            train_op = self.optimizer.minimize(
                loss=loss,
                global_step=tf.train.get_global_step())
        
        # Evaluation metric
        eval_metric_ops = tf.count_nonzero(tf.equal(labels, predictions["classes"]))
        
        return features, labels, train_op, eval_metric_ops, loss

    def process_x(self, raw_x_batch):
        """Process features: normalize to [0, 1] range."""
        processed_x = np.array(raw_x_batch, dtype=np.float32)
        # Normalize pixel values to [0, 1]
        return processed_x / 255.0

    def process_y(self, raw_y_batch):
        """Process labels."""
        return np.array(raw_y_batch)


# Example usage in main.py:
"""
# Import the enhanced model
from femnist.enhanced_cnn import ClientModel

# Create client model with additional parameters
client_model = ClientModel(
    args.seed, 
    args.lr, 
    62,  # 62 classes for FEMNIST
    optimizer_type='adam',
    dropout_rate=0.5,
    use_batch_norm=True
)
"""
