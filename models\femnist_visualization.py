#!/usr/bin/env python
"""
FEMNIST Results Visualization Script

This script generates visualizations to compare the performance of different FEMNIST models
based on metrics collected during the experiments.
"""

import os
import sys
import argparse
import pandas as pd
import numpy as np
import matplotlib.pyplot as plt
import matplotlib.ticker as ticker
from matplotlib.ticker import MaxNLocator
import seaborn as sns
from pathlib import Path

# Configure plot style
plt.style.use('seaborn-v0_8-whitegrid')
sns.set_palette("colorblind")
plt.rcParams.update({
    'figure.figsize': (12, 8),
    'font.size': 14,
    'axes.labelsize': 14,
    'axes.titlesize': 16,
    'xtick.labelsize': 12,
    'ytick.labelsize': 12,
    'legend.fontsize': 12,
    'legend.title_fontsize': 14
})

# Define experiment names and labels for plots
EXPERIMENT_LABELS = {
    "original": "Original CNN",
    "enhanced_sgd": "Enhanced CNN + SGD",
    "enhanced_adam": "Enhanced CNN + Adam",
    "enhanced_fedavg": "Enhanced CNN + FedAvg"
}

# Color scheme for consistency
COLORS = {
    "original": "#1f77b4",       # Blue
    "enhanced_sgd": "#ff7f0e",   # Orange
    "enhanced_adam": "#2ca02c",  # Green
    "enhanced_fedavg": "#d62728" # Red
}

def load_experiment_data(metrics_dir, experiment_name):
    """Load experiment data from CSV file."""
    csv_path = os.path.join(metrics_dir, experiment_name, f"{experiment_name}_results.csv")
    if not os.path.exists(csv_path):
        print(f"Warning: Results file not found at {csv_path}")
        return None
    
    try:
        df = pd.read_csv(csv_path)
        return df
    except Exception as e:
        print(f"Error loading data from {csv_path}: {e}")
        return None

def plot_accuracy_comparison(experiment_data, output_dir, title_suffix=""):
    """Generate a line plot comparing test accuracy across experiments."""
    plt.figure(figsize=(12, 8))
    
    for experiment, df in experiment_data.items():
        if df is not None and 'test_accuracy' in df.columns:
            plt.plot(
                df['round'], 
                df['test_accuracy'], 
                label=EXPERIMENT_LABELS.get(experiment, experiment),
                color=COLORS.get(experiment),
                marker='o',
                markersize=5,
                alpha=0.8
            )
    
    plt.title(f'Test Accuracy Comparison{title_suffix}')
    plt.xlabel('Communication Round')
    plt.ylabel('Test Accuracy')
    plt.grid(True, linestyle='--', alpha=0.7)
    plt.legend(loc='lower right')
    plt.gca().xaxis.set_major_locator(MaxNLocator(integer=True))
    
    # Save figure
    output_path = os.path.join(output_dir, 'accuracy_comparison.png')
    plt.savefig(output_path, dpi=300, bbox_inches='tight')
    print(f"Saved accuracy comparison plot to {output_path}")
    
    plt.close()

def plot_loss_comparison(experiment_data, output_dir, title_suffix=""):
    """Generate a line plot comparing test loss across experiments."""
    plt.figure(figsize=(12, 8))
    
    for experiment, df in experiment_data.items():
        if df is not None and 'test_loss' in df.columns:
            plt.plot(
                df['round'], 
                df['test_loss'], 
                label=EXPERIMENT_LABELS.get(experiment, experiment),
                color=COLORS.get(experiment),
                marker='o',
                markersize=5,
                alpha=0.8
            )
    
    plt.title(f'Test Loss Comparison{title_suffix}')
    plt.xlabel('Communication Round')
    plt.ylabel('Test Loss')
    plt.grid(True, linestyle='--', alpha=0.7)
    plt.legend(loc='upper right')
    plt.gca().xaxis.set_major_locator(MaxNLocator(integer=True))
    
    # Save figure
    output_path = os.path.join(output_dir, 'loss_comparison.png')
    plt.savefig(output_path, dpi=300, bbox_inches='tight')
    print(f"Saved loss comparison plot to {output_path}")
    
    plt.close()

def plot_final_performance_comparison(experiment_data, output_dir, title_suffix=""):
    """Generate a bar chart comparing final test accuracy across experiments."""
    # Extract final performance metrics
    final_accuracy = {}
    
    for experiment, df in experiment_data.items():
        if df is not None and 'test_accuracy' in df.columns:
            # Get the last row for each experiment
            final_row = df.iloc[-1]
            final_accuracy[experiment] = final_row['test_accuracy']
    
    if not final_accuracy:
        print("No test accuracy data available for comparison")
        return
    
    # Sort experiments by accuracy
    sorted_experiments = sorted(final_accuracy.items(), key=lambda x: x[1], reverse=True)
    experiment_names = [EXPERIMENT_LABELS.get(exp, exp) for exp, _ in sorted_experiments]
    accuracy_values = [acc for _, acc in sorted_experiments]
    experiment_keys = [exp for exp, _ in sorted_experiments]
    
    # Create bar plot
    plt.figure(figsize=(12, 8))
    bars = plt.bar(
        experiment_names, 
        accuracy_values,
        color=[COLORS.get(exp, "#333333") for exp in experiment_keys],
        alpha=0.8
    )
    
    # Add value labels on top of bars
    for bar in bars:
        height = bar.get_height()
        plt.text(
            bar.get_x() + bar.get_width()/2.,
            height + 0.005,
            f'{height:.4f}',
            ha='center', 
            va='bottom',
            fontsize=12
        )
    
    plt.title(f'Final Test Accuracy Comparison{title_suffix}')
    plt.ylabel('Test Accuracy')
    plt.ylim(0, max(accuracy_values) + 0.05)  # Add padding for text labels
    plt.grid(True, axis='y', linestyle='--', alpha=0.7)
    
    # Save figure
    output_path = os.path.join(output_dir, 'final_accuracy_comparison.png')
    plt.savefig(output_path, dpi=300, bbox_inches='tight')
    print(f"Saved final accuracy comparison plot to {output_path}")
    
    plt.close()

def plot_convergence_analysis(experiment_data, output_dir, accuracy_threshold=0.7, title_suffix=""):
    """
    Generate a plot showing how quickly each model reaches certain accuracy thresholds.
    """
    plt.figure(figsize=(12, 8))
    
    # Find rounds to reach threshold for each experiment
    threshold_rounds = {}
    
    for experiment, df in experiment_data.items():
        if df is not None and 'test_accuracy' in df.columns:
            # Find first round where accuracy exceeds threshold
            above_threshold = df[df['test_accuracy'] >= accuracy_threshold]
            if not above_threshold.empty:
                first_round = above_threshold['round'].iloc[0]
                threshold_rounds[experiment] = first_round
            else:
                # If never reaches threshold, use max round number
                threshold_rounds[experiment] = df['round'].max()
    
    if not threshold_rounds:
        print(f"No experiments reached the accuracy threshold of {accuracy_threshold}")
        return
    
    # Sort experiments by rounds to threshold
    sorted_experiments = sorted(threshold_rounds.items(), key=lambda x: x[1])
    experiment_names = [EXPERIMENT_LABELS.get(exp, exp) for exp, _ in sorted_experiments]
    round_values = [rounds for _, rounds in sorted_experiments]
    experiment_keys = [exp for exp, _ in sorted_experiments]
    
    # Create bar plot
    plt.figure(figsize=(12, 8))
    bars = plt.bar(
        experiment_names, 
        round_values,
        color=[COLORS.get(exp, "#333333") for exp in experiment_keys],
        alpha=0.8
    )
    
    # Add value labels on top of bars
    for bar in bars:
        height = bar.get_height()
        plt.text(
            bar.get_x() + bar.get_width()/2.,
            height + 0.5,
            f'{int(height)}',
            ha='center', 
            va='bottom',
            fontsize=12
        )
    
    plt.title(f'Rounds to Reach {accuracy_threshold:.2f} Accuracy{title_suffix}')
    plt.ylabel('Number of Communication Rounds')
    plt.ylim(0, max(round_values) + max(round_values) * 0.1)  # Add padding for text labels
    plt.grid(True, axis='y', linestyle='--', alpha=0.7)
    
    # Save figure
    output_path = os.path.join(output_dir, f'convergence_threshold_{int(accuracy_threshold*100)}.png')
    plt.savefig(output_path, dpi=300, bbox_inches='tight')
    print(f"Saved convergence analysis plot to {output_path}")
    
    plt.close()

def generate_summary_table(experiment_data, output_dir):
    """Generate a summary table with key metrics for each experiment."""
    summary_data = []
    
    # Define metrics to include in the summary
    metrics = ['test_accuracy', 'test_loss', 'train_accuracy', 'train_loss']
    
    for experiment, df in experiment_data.items():
        if df is None or df.empty:
            continue
        
        # Get the first and last row for each experiment
        first_row = df.iloc[0]
        last_row = df.iloc[-1]
        
        # Calculate improvement and convergence metrics
        exp_data = {
            'Experiment': EXPERIMENT_LABELS.get(experiment, experiment),
            'Final Rounds': int(last_row['round'])
        }
        
        for metric in metrics:
            if metric in df.columns:
                exp_data[f'Initial {metric}'] = first_row[metric]
                exp_data[f'Final {metric}'] = last_row[metric]
                exp_data[f'Improvement {metric}'] = last_row[metric] - first_row[metric]
        
        summary_data.append(exp_data)
    
    if not summary_data:
        print("No data available for summary table")
        return
    
    # Create DataFrame from gathered data
    summary_df = pd.DataFrame(summary_data)
    
    # Save summary to CSV
    output_path = os.path.join(output_dir, 'experiment_summary.csv')
    summary_df.to_csv(output_path, index=False)
    print(f"Saved summary table to {output_path}")
    
    # Also print summary to console
    print("\n" + "="*100)
    print("EXPERIMENT SUMMARY")
    print("="*100)
    print(summary_df.to_string(index=False))
    print("="*100 + "\n")

def main():
    """Main function to generate visualizations."""
    parser = argparse.ArgumentParser(description='Generate visualizations for FEMNIST experiments')
    
    parser.add_argument('--metrics-dir', type=str, default='metrics',
                        help='directory containing metrics folders')
    parser.add_argument('--output-dir', type=str, default='visualizations',
                        help='directory to save visualization outputs')
    parser.add_argument('--experiments', type=str, nargs='+', 
                        default=['original', 'enhanced_sgd', 'enhanced_adam', 'enhanced_fedavg'],
                        help='experiments to include in visualizations')
    parser.add_argument('--title-suffix', type=str, default='',
                        help='suffix to add to plot titles')
    
    args = parser.parse_args()
    
    # Create output directory
    os.makedirs(args.output_dir, exist_ok=True)
    
    # Load experiment data
    experiment_data = {}
    for experiment in args.experiments:
        data = load_experiment_data(args.metrics_dir, experiment)
        if data is not None:
            experiment_data[experiment] = data
    
    if not experiment_data:
        print("No experiment data found. Please check the metrics directory and experiment names.")
        sys.exit(1)
    
    # Generate visualizations
    plot_accuracy_comparison(experiment_data, args.output_dir, args.title_suffix)
    plot_loss_comparison(experiment_data, args.output_dir, args.title_suffix)
    plot_final_performance_comparison(experiment_data, args.output_dir, args.title_suffix)
    
    # Generate convergence analysis for different thresholds
    for threshold in [0.5, 0.6, 0.7, 0.75]:
        plot_convergence_analysis(experiment_data, args.output_dir, threshold, args.title_suffix)
    
    # Generate summary table
    generate_summary_table(experiment_data, args.output_dir)
    
    print(f"\nAll visualizations have been generated in {os.path.abspath(args.output_dir)}")

if __name__ == "__main__":
    main()
