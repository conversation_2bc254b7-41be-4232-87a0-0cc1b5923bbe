import matplotlib.pyplot as plt
import numpy as np
import pandas as pd

# Data for weighted average accuracy
weighted_data = pd.read_csv('weighted_results.csv')

# Data for simple average accuracy (from our previous analysis)
rounds = [0, 100, 200, 300, 400, 500, 600, 700, 800, 900, 1000, 1500, 2000]
simple_accuracy = [0.0112, 0.2272, 0.4643, 0.5373, 0.6233, 0.6667, 0.7145, 0.7426, 0.7267, 0.7441, 0.7165, 0.6996, 0.7794]

# Create the plot
plt.figure(figsize=(12, 8))
plt.plot(weighted_data['Round'], weighted_data['Weighted_Accuracy'], 'b-o', linewidth=2, markersize=8, label='Weighted Average Accuracy')
plt.plot(rounds, simple_accuracy, 'r-^', linewidth=2, markersize=8, label='Simple Average Accuracy')

# Add labels and title
plt.xlabel('Number of Rounds', fontsize=14)
plt.ylabel('Accuracy', fontsize=14)
plt.title('Weighted vs. Simple Average Accuracy Comparison', fontsize=16)

# Add grid and legend
plt.grid(True, alpha=0.3)
plt.legend(fontsize=12)

# Mark the key points
# Early convergence point (round 700)
plt.axvline(x=700, color='purple', linestyle='--', alpha=0.7, label='Early Convergence (Round 700)')
plt.plot(700, weighted_data.loc[weighted_data['Round'] == 700, 'Weighted_Accuracy'].values[0], 'bo', markersize=10)
plt.plot(700, simple_accuracy[rounds.index(700)], 'ro', markersize=10)

# Final point (round 2000)
plt.axvline(x=2000, color='green', linestyle='--', alpha=0.7, label='Final Point (Round 2000)')
plt.plot(2000, weighted_data.loc[weighted_data['Round'] == 2000, 'Weighted_Accuracy'].values[0], 'bo', markersize=10)
plt.plot(2000, simple_accuracy[rounds.index(2000)], 'ro', markersize=10)

# Add annotations for the differences at key points
weighted_700 = weighted_data.loc[weighted_data['Round'] == 700, 'Weighted_Accuracy'].values[0]
simple_700 = simple_accuracy[rounds.index(700)]
diff_700 = weighted_700 - simple_700

weighted_2000 = weighted_data.loc[weighted_data['Round'] == 2000, 'Weighted_Accuracy'].values[0]
simple_2000 = simple_accuracy[rounds.index(2000)]
diff_2000 = weighted_2000 - simple_2000

plt.annotate(f'Difference: {diff_700:.4f}', 
             xy=(700, (weighted_700 + simple_700)/2), 
             xytext=(500, (weighted_700 + simple_700)/2 + 0.05),
             arrowprops=dict(facecolor='black', shrink=0.05, width=1, headwidth=5), 
             fontsize=10)

plt.annotate(f'Difference: {diff_2000:.4f}', 
             xy=(2000, (weighted_2000 + simple_2000)/2), 
             xytext=(1800, (weighted_2000 + simple_2000)/2 + 0.05),
             arrowprops=dict(facecolor='black', shrink=0.05, width=1, headwidth=5), 
             fontsize=10)

# Highlight the three phases
plt.axvspan(0, 700, alpha=0.1, color='yellow', label='Fast Convergence Phase')
plt.axvspan(700, 1500, alpha=0.1, color='orange', label='Fluctuation Phase')
plt.axvspan(1500, 2000, alpha=0.1, color='lightgreen', label='Re-optimization Phase')

# Set axis limits
plt.xlim(0, 2100)
plt.ylim(0, 0.85)

# Add a table with the data
table_data = []
for i, r in enumerate(rounds):
    table_data.append([r, f"{weighted_data.loc[weighted_data['Round'] == r, 'Weighted_Accuracy'].values[0]:.4f}", 
                      f"{simple_accuracy[i]:.4f}", 
                      f"{weighted_data.loc[weighted_data['Round'] == r, 'Weighted_Accuracy'].values[0] - simple_accuracy[i]:.4f}"])

plt.table(cellText=table_data,
          colLabels=['Round', 'Weighted Acc', 'Simple Acc', 'Difference'],
          loc='bottom',
          bbox=[0.0, -0.50, 1.0, 0.3])

plt.subplots_adjust(bottom=0.35)

# Save the figure
plt.savefig('accuracy_comparison.png', dpi=300, bbox_inches='tight')
plt.close()

# Create a second plot without the table for better visualization
plt.figure(figsize=(12, 8))
plt.plot(weighted_data['Round'], weighted_data['Weighted_Accuracy'], 'b-o', linewidth=2, markersize=8, label='Weighted Average Accuracy')
plt.plot(rounds, simple_accuracy, 'r-^', linewidth=2, markersize=8, label='Simple Average Accuracy')

# Add labels and title
plt.xlabel('Number of Rounds', fontsize=14)
plt.ylabel('Accuracy', fontsize=14)
plt.title('Weighted vs. Simple Average Accuracy Comparison', fontsize=16)

# Add grid and legend
plt.grid(True, alpha=0.3)
plt.legend(fontsize=12)

# Mark the key points
plt.axvline(x=700, color='purple', linestyle='--', alpha=0.7, label='Early Convergence (Round 700)')
plt.axvline(x=2000, color='green', linestyle='--', alpha=0.7, label='Final Point (Round 2000)')

# Highlight the three phases
plt.axvspan(0, 700, alpha=0.1, color='yellow', label='Fast Convergence Phase')
plt.axvspan(700, 1500, alpha=0.1, color='orange', label='Fluctuation Phase')
plt.axvspan(1500, 2000, alpha=0.1, color='lightgreen', label='Re-optimization Phase')

# Set axis limits
plt.xlim(0, 2100)
plt.ylim(0, 0.85)

# Add a text box with key observations
textstr = '\n'.join((
    'Key Observations:',
    '1. Weighted accuracy is generally higher than simple average',
    f'2. At round 700: Weighted={weighted_700:.4f}, Simple={simple_700:.4f}, Diff={diff_700:.4f}',
    f'3. At round 2000: Weighted={weighted_2000:.4f}, Simple={simple_2000:.4f}, Diff={diff_2000:.4f}',
    '4. Largest differences occur during the fluctuation phase',
    '5. Both methods show the three-phase convergence pattern'
))

props = dict(boxstyle='round', facecolor='wheat', alpha=0.5)
plt.text(1050, 0.15, textstr, fontsize=10, verticalalignment='bottom', bbox=props)

# Save the figure
plt.savefig('accuracy_comparison_clean.png', dpi=300, bbox_inches='tight')
plt.close()

print("Comparison plots generated: accuracy_comparison.png and accuracy_comparison_clean.png")
