import os
import docx
import requests
import uuid
import json
import time
from docx import Document
from docx.shared import Pt
from docx.enum.text import WD_PARAGRAPH_ALIGNMENT

# Azure Translator API configuration
# You'll need to replace these with your own values
AZURE_SUBSCRIPTION_KEY = "YOUR_SUBSCRIPTION_KEY"
AZURE_REGION = "eastus"  # replace with your region
TRANSLATOR_ENDPOINT = "https://api.cognitive.microsofttranslator.com"

def configure_translator():
    """Ask for Azure Translator API key if not set"""
    global AZURE_SUBSCRIPTION_KEY
    if AZURE_SUBSCRIPTION_KEY == "YOUR_SUBSCRIPTION_KEY":
        print("Azure Translator API key is required.")
        AZURE_SUBSCRIPTION_KEY = input("Enter your Azure Translator API key: ")

def translate_text_azure(text, to_lang='zh-Hans'):
    """Translate text using Azure Translator API"""
    if not text.strip():
        return ""

    # Construct request
    path = '/translate'
    constructed_url = TRANSLATOR_ENDPOINT + path

    # Request parameters
    params = {
        'api-version': '3.0',
        'to': to_lang
    }

    # Request headers
    headers = {
        'Ocp-Apim-Subscription-Key': AZURE_SUBSCRIPTION_KEY,
        'Ocp-Apim-Subscription-Region': AZURE_REGION,
        'Content-type': 'application/json',
        'X-ClientTraceId': str(uuid.uuid4())
    }

    # Request body
    body = [{
        'text': text
    }]

    # Make the API call
    try:
        response = requests.post(constructed_url, params=params, headers=headers, json=body)
        response.raise_for_status()  # Raise exception for 4XX/5XX status codes
        result = response.json()
        
        if result and len(result) > 0 and 'translations' in result[0]:
            return result[0]['translations'][0]['text']
        return text
    except Exception as e:
        print(f"Translation error: {e}")
        return text

def translate_text_mock(text, to_lang='zh-Hans'):
    """Mock translator for testing without API key"""
    # This function simulates translation for testing purposes
    # It just adds "[ZH]" before the text to indicate it would be translated
    time.sleep(0.1)  # Simulate API call delay
    return f"[ZH] {text}"

def preserve_run_properties(original_run, new_run):
    """Copy all formatting properties from original run to new run"""
    # Copy basic properties
    new_run.bold = original_run.bold
    new_run.italic = original_run.italic
    new_run.underline = original_run.underline
    new_run.strike = original_run.strike
    new_run.font.highlight_color = original_run.font.highlight_color
    
    # Copy font properties
    if original_run.font.name:
        new_run.font.name = original_run.font.name
    if original_run.font.size:
        new_run.font.size = original_run.font.size
    if original_run.font.color.rgb:
        new_run.font.color.rgb = original_run.font.color.rgb
    
    # Copy other properties that might be present
    new_run.style = original_run.style
    
    return new_run

def translate_paragraph(paragraph, use_mock=False):
    """Translate a paragraph while preserving its formatting"""
    if not paragraph.text.strip():
        return  # Skip empty paragraphs
    
    # Store original alignment
    alignment = paragraph.alignment
    
    # Store original run information with text and formatting
    original_runs = []
    for run in paragraph.runs:
        original_runs.append({
            'text': run.text,
            'obj': run
        })
    
    # Combine all text for translation
    full_text = paragraph.text
    
    # Translate the full text
    if use_mock:
        translated_text = translate_text_mock(full_text)
    else:
        translated_text = translate_text_azure(full_text)
    
    # Clear the paragraph
    p = paragraph._p
    for _ in range(len(paragraph.runs)):
        if len(p) > 0:  # Check if there are still runs to remove
            p.remove(p[0])
    
    # Add the translated text with best-effort formatting
    # Since we can't perfectly map original formatting positions to translated text,
    # we'll apply the most dominant formatting to the entire translation
    
    # If there was only one run, or multiple runs with the same formatting,
    # we can just apply that formatting to the entire translated text
    if original_runs:
        new_run = paragraph.add_run(translated_text)
        # Apply formatting from the first (usually main) run
        preserve_run_properties(original_runs[0]['obj'], new_run)
    else:
        paragraph.add_run(translated_text)
    
    # Restore original alignment
    paragraph.alignment = alignment

def translate_table(table, use_mock=False):
    """Translate all cells in a table"""
    for row in table.rows:
        for cell in row.cells:
            for paragraph in cell.paragraphs:
                translate_paragraph(paragraph, use_mock)

def translate_document(input_path, output_path, use_mock=False):
    """Translate the entire document while preserving formatting"""
    try:
        doc = Document(input_path)
        
        # Check if the document was loaded successfully
        if not doc:
            print(f"Failed to load document from {input_path}")
            return False
        
        # Translate each paragraph
        print("Translating paragraphs...")
        for i, paragraph in enumerate(doc.paragraphs):
            if i % 10 == 0:
                print(f"Processing paragraph {i+1}/{len(doc.paragraphs)}")
            translate_paragraph(paragraph, use_mock)
        
        # Translate tables
        print("Translating tables...")
        for i, table in enumerate(doc.tables):
            print(f"Processing table {i+1}/{len(doc.tables)}")
            translate_table(table, use_mock)
        
        # Save the translated document
        print(f"Saving translated document to {output_path}")
        doc.save(output_path)
        return True
    except Exception as e:
        print(f"Error in document translation: {e}")
        import traceback
        traceback.print_exc()
        return False

def main():
    input_file = r"C:\Users\<USER>\Desktop\private-ai\leaf\CV.docx"
    output_file = r"C:\Users\<USER>\Desktop\private-ai\leaf\CV_Chinese_Azure.docx"
    
    print(f"Starting translation of {input_file}")
    
    # Determine whether to use mock translator or real API
    use_mock = True  # Change to False to use real Azure API
    
    if not use_mock:
        configure_translator()
    else:
        print("Running in MOCK mode - no actual translation will occur")
        print("Set use_mock = False in the code to use the real Azure Translator API")
    
    success = translate_document(input_file, output_file, use_mock)
    
    if success:
        print("Translation complete!")
        print(f"Translated document saved to: {output_file}")
    else:
        print("Translation failed. Check the error messages above.")

if __name__ == "__main__":
    main() 