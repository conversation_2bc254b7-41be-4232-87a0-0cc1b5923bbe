import pandas as pd
import numpy as np

# 读取metrics_stat.csv文件，只读取需要的列以减少内存使用
file_path = 'models/metrics/metrics_stat.csv'
print("正在读取CSV文件...")
df = pd.read_csv(file_path, usecols=['round_number', 'accuracy', 'loss', 'set'])

# 按照round_number排序
df = df.sort_values(by='round_number')

# 分离训练集和测试集数据
train_df = df[df['set'] == 'train']
test_df = df[df['set'] == 'test']

# 计算每一轮的平均accuracy和loss
print("正在处理数据...")
train_metrics = train_df.groupby('round_number').agg({'accuracy': 'mean', 'loss': 'mean'}).reset_index()
test_metrics = test_df.groupby('round_number').agg({'accuracy': 'mean', 'loss': 'mean'}).reset_index()

# 打印数据的基本信息
print("\n=== 模型训练概况 ===")
print(f"训练轮数: {len(train_metrics)}")
print(f"起始轮数: {train_metrics['round_number'].min()}")
print(f"结束轮数: {train_metrics['round_number'].max()}")
print(f"\n最终训练集准确率: {train_metrics['accuracy'].iloc[-1]:.4f}")
print(f"最终测试集准确率: {test_metrics['accuracy'].iloc[-1]:.4f}")
print(f"最高测试集准确率: {test_metrics['accuracy'].max():.4f} (轮数: {test_metrics.loc[test_metrics['accuracy'].idxmax(), 'round_number']})")

# 定义一个简单的方法来检测收敛
# 使用移动平均来平滑准确率曲线
window_size = 5  # 移动平均窗口大小
if len(test_metrics) > window_size:
    test_metrics['accuracy_ma'] = test_metrics['accuracy'].rolling(window=window_size).mean()
    
    # 计算移动平均的变化率
    test_metrics['accuracy_change'] = test_metrics['accuracy_ma'].diff().abs()
    
    # 找到一个点，其后的变化非常小
    convergence_threshold = 0.001  # 变化阈值
    consecutive_stable_rounds = 5  # 需要连续稳定的轮数
    
    # 初始化
    convergence_round = None
    convergence_accuracy = None
    
    # 从一定轮数后开始判断收敛（比如从总轮数的20%开始）
    start_idx = int(len(test_metrics) * 0.2)
    
    # 寻找收敛点
    for i in range(start_idx, len(test_metrics) - consecutive_stable_rounds + 1):
        # 计算该点之后consecutive_stable_rounds轮的平均变化
        avg_change = test_metrics['accuracy_change'].iloc[i:i+consecutive_stable_rounds].mean()
        if avg_change < convergence_threshold and not pd.isna(avg_change):
            convergence_round = test_metrics['round_number'].iloc[i]
            convergence_accuracy = test_metrics['accuracy'].iloc[i]
            break
    
    # 如果上述方法没有找到收敛点，尝试使用最大准确率点附近
    if convergence_round is None:
        max_accuracy_idx = test_metrics['accuracy'].idxmax()
        convergence_round = test_metrics['round_number'].iloc[max_accuracy_idx]
        convergence_accuracy = test_metrics['accuracy'].iloc[max_accuracy_idx]
else:
    # 如果数据点太少，使用最大准确率点
    max_accuracy_idx = test_metrics['accuracy'].idxmax()
    convergence_round = test_metrics['round_number'].iloc[max_accuracy_idx]
    convergence_accuracy = test_metrics['accuracy'].iloc[max_accuracy_idx]

# 打印结果
print(f"\n=== 模型收敛分析 ===")
print(f"模型在第 {convergence_round} 轮收敛")
print(f"收敛后的准确率约为: {convergence_accuracy:.4f}")

# 准确率的稳定性分析
if len(test_metrics) > 20:
    # 计算收敛后的平均准确率和标准差
    convergence_idx = test_metrics[test_metrics['round_number'] >= convergence_round].index
    if len(convergence_idx) > 0:
        post_convergence_acc = test_metrics.loc[convergence_idx, 'accuracy']
        print(f"\n收敛后准确率平均值: {post_convergence_acc.mean():.4f}")
        print(f"收敛后准确率标准差: {post_convergence_acc.std():.4f}")
        
        # 输出准确率的分位数统计
        percentiles = [25, 50, 75, 90, 95]
        print("\n收敛后准确率分位数统计:")
        for p in percentiles:
            print(f"{p}%分位: {np.percentile(post_convergence_acc, p):.4f}")

# 保存关键统计数据到CSV文件
print("\n保存处理后的数据...")
summary_df = pd.DataFrame({
    'metric': ['total_rounds', 'convergence_round', 'convergence_accuracy', 
               'final_train_accuracy', 'final_test_accuracy', 'max_test_accuracy',
               'max_accuracy_round'],
    'value': [len(train_metrics), convergence_round, convergence_accuracy,
              train_metrics['accuracy'].iloc[-1], test_metrics['accuracy'].iloc[-1],
              test_metrics['accuracy'].max(), test_metrics.loc[test_metrics['accuracy'].idxmax(), 'round_number']]
})
summary_df.to_csv('model_convergence_summary.csv', index=False)

print("\n分析完成。汇总统计已保存为 model_convergence_summary.csv") 