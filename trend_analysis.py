import pandas as pd
import matplotlib.pyplot as plt
import numpy as np

# 读取数据
data = pd.read_csv('train_test_weighted_results.csv')

# 计算每轮的变化率（相对于上一轮）
data['Train_Change'] = data['Train_Weighted_Accuracy'].diff()
data['Test_Change'] = data['Test_Weighted_Accuracy'].diff()

# 计算训练集和测试集变化的差异
data['Change_Diff'] = data['Train_Change'] - data['Test_Change']

# 打印结果
print("轮数\t训练集准确率\t测试集准确率\t训练集变化\t测试集变化\t变化差异")
print("-" * 80)
for i in range(1, len(data)):  # 从第1轮开始，因为第0轮没有变化率
    print(f"{data['Round'][i]}\t{data['Train_Weighted_Accuracy'][i]:.4f}\t{data['Test_Weighted_Accuracy'][i]:.4f}\t{data['Train_Change'][i]:.4f}\t{data['Test_Change'][i]:.4f}\t{data['Change_Diff'][i]:.4f}")

# 创建变化率对比图
plt.figure(figsize=(12, 8))
plt.plot(data['Round'][1:], data['Train_Change'][1:], 'b-o', linewidth=2, markersize=8, label='训练集准确率变化')
plt.plot(data['Round'][1:], data['Test_Change'][1:], 'r-^', linewidth=2, markersize=8, label='测试集准确率变化')

# 添加标签和标题
plt.xlabel('轮数', fontsize=14)
plt.ylabel('准确率变化（相对于上一轮）', fontsize=14)
plt.title('训练集与测试集准确率变化趋势对比', fontsize=16)

# 添加网格和图例
plt.grid(True, alpha=0.3)
plt.legend(fontsize=12)

# 添加零线
plt.axhline(y=0, color='black', linestyle='-', alpha=0.3)

# 标记三个阶段
plt.axvspan(0, 700, alpha=0.1, color='yellow', label='快速收敛阶段')
plt.axvspan(700, 1500, alpha=0.1, color='orange', label='波动阶段')
plt.axvspan(1500, 2000, alpha=0.1, color='lightgreen', label='再优化阶段')

# 设置坐标轴范围
plt.xlim(0, 2100)

# 保存图表
plt.tight_layout()
plt.savefig('accuracy_change_comparison.png', dpi=300, bbox_inches='tight')
plt.close()

# 创建变化差异图
plt.figure(figsize=(12, 6))
plt.bar(data['Round'][1:], data['Change_Diff'][1:], color='purple', alpha=0.7)

# 添加标签和标题
plt.xlabel('轮数', fontsize=14)
plt.ylabel('变化差异（训练集变化 - 测试集变化）', fontsize=14)
plt.title('训练集与测试集准确率变化差异', fontsize=16)

# 添加网格
plt.grid(True, alpha=0.3, axis='y')

# 添加零线
plt.axhline(y=0, color='black', linestyle='-', alpha=0.5)

# 标记三个阶段
plt.axvspan(0, 700, alpha=0.1, color='yellow', label='快速收敛阶段')
plt.axvspan(700, 1500, alpha=0.1, color='orange', label='波动阶段')
plt.axvspan(1500, 2000, alpha=0.1, color='lightgreen', label='再优化阶段')

# 设置坐标轴范围
plt.xlim(0, 2100)

# 添加图例
plt.legend(fontsize=12)

# 保存图表
plt.tight_layout()
plt.savefig('accuracy_change_difference.png', dpi=300, bbox_inches='tight')
plt.close()

# 计算相关性
correlation = np.corrcoef(data['Train_Change'][1:], data['Test_Change'][1:])[0, 1]
print(f"\n训练集和测试集准确率变化的相关系数: {correlation:.4f}")

# 计算趋势一致性
same_direction = sum((data['Train_Change'][1:] > 0) == (data['Test_Change'][1:] > 0))
total_rounds = len(data) - 1
consistency_percentage = (same_direction / total_rounds) * 100
print(f"训练集和测试集准确率变化方向一致的轮数比例: {consistency_percentage:.2f}% ({same_direction}/{total_rounds})")

# 分阶段分析
# 快速收敛阶段 (100-700)
early_indices = (data['Round'] >= 100) & (data['Round'] <= 700)
early_corr = np.corrcoef(data.loc[early_indices, 'Train_Change'], data.loc[early_indices, 'Test_Change'])[0, 1]
early_same_dir = sum((data.loc[early_indices, 'Train_Change'] > 0) == (data.loc[early_indices, 'Test_Change'] > 0))
early_total = sum(early_indices)
early_consistency = (early_same_dir / early_total) * 100

# 波动阶段 (800-1500)
mid_indices = (data['Round'] >= 800) & (data['Round'] <= 1500)
mid_corr = np.corrcoef(data.loc[mid_indices, 'Train_Change'], data.loc[mid_indices, 'Test_Change'])[0, 1]
mid_same_dir = sum((data.loc[mid_indices, 'Train_Change'] > 0) == (data.loc[mid_indices, 'Test_Change'] > 0))
mid_total = sum(mid_indices)
mid_consistency = (mid_same_dir / mid_total) * 100

# 再优化阶段 (1500-2000)
late_indices = (data['Round'] > 1500)
late_corr = np.corrcoef(data.loc[late_indices, 'Train_Change'], data.loc[late_indices, 'Test_Change'])[0, 1]
late_same_dir = sum((data.loc[late_indices, 'Train_Change'] > 0) == (data.loc[late_indices, 'Test_Change'] > 0))
late_total = sum(late_indices)
late_consistency = (late_same_dir / late_total) * 100

print("\n分阶段分析:")
print(f"快速收敛阶段 (100-700): 相关系数 = {early_corr:.4f}, 方向一致性 = {early_consistency:.2f}%")
print(f"波动阶段 (800-1500): 相关系数 = {mid_corr:.4f}, 方向一致性 = {mid_consistency:.2f}%")
print(f"再优化阶段 (1500-2000): 相关系数 = {late_corr:.4f}, 方向一致性 = {late_consistency:.2f}%")

print("\n图表已生成: accuracy_change_comparison.png 和 accuracy_change_difference.png")
