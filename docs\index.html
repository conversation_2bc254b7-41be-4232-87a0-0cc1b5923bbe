<!DOCTYPE html>
<html>

<head>

	<meta charset="utf-8">
	<meta http-equiv="X-UA-Compatible" content="IE=edge">
	<meta name="viewport" content="width=device-width, initial-scale=1">
	<link rel="icon" href="webpage/icon.png">

	<title>LEAF</title>

	<link rel="stylesheet" href="webpage/assets/main.css">
	<link rel="stylesheet" href="webpage/assets/header-basic.css">
	<link href="https://fonts.googleapis.com/css?family=Roboto" rel="stylesheet">
	
</head>

<body>
	<div class="background">
	<div class="toptitle" style="position:relative;top:-20px">
        <h1 style="color:#196619">
            LEAF <img src="webpage/icon.png" width="70" height="70" align="center"
                        style="position: relative; top: -10px;">
        </h1>
		<h2>A Benchmark for Federated Settings</h2>
        <br/>
    </div>
    </div>
    <div class="title" style="margin-top:50px">

        <span class="description">
            LEAF is a benchmarking framework for learning in federated settings, with applications including federated learning, multi-task learning, meta-learning, and on-device learning. Future releases will include additional tasks and datasets. Please contact <a href="https://scaldas.xyz">Sebastian Caldas</a></h3> with questions or to contribute to the benchmark.
        </span>		
        <br/>
        <br/>
		<h3><a href="build/html/index.html">Get Started</a> <a href="https://github.com/TalwalkarLab/leaf">GitHub</a></h3>
        
    </div>

	<div class="menu"  style="position:relative;top:40px">
		<button class="collapsible">
			<span style="position:relative;top:12px;"><font size="6">FEMNIST</font></span>
			<span style="position:relative;top:12px;" class="showHide"><font size="3">Image Classification</font></span>
			<a href="https://github.com/TalwalkarLab/leaf" style="float:right"><img src="webpage/images/femnist.png" alt="Logo" height="50"></a>
		</button>
		<div class="content">
			<div class="vertspace">
				<div class="infobox">
					<div class="infobox-1">
						<p>
							<b>Statistics</b><br>
							3,550 users<br>
							80,5263 samples (total)<br>
							226.83 samples per user (mean)<br>
							num_samples (std): 88.94<br>
							num_samples (std/mean): 0.39<br>
						</p>
					</div>
					<div class="infobox-2">
						<img src="webpage/images/femnist_hist.png" alt="Histogram" height="250px">
					</div>
				</div>
			</div>
		</div>
		<button class="collapsible">
			<span style="position:relative;top:12px;"><font size="6">Shakespeare</font></span>
			<span style="position:relative;top:12px;" class="showHide"><font size="3">Next Character Prediction</font></span>
			<a href="https://github.com/TalwalkarLab/leaf" style="float:right"><img src="webpage/images/shakespeare.png" alt="Logo" height="50"></a>
		</button>
		<div class="content">
				<div class="vertspace">
					<div class="infobox">
						<div class="infobox-1">
							<p>
								<b>Statistics</b><br>
								1,129 users<br>
								4,226,15 samples (total)<br>
								3,743.2 samples per user (mean)<br>
								num_samples (std): 6,212.26<br>
								num_samples (std/mean): 1.65<br>
							</p>
						</div>
						<div class="infobox-2">
							<img src="webpage/images/shakespeare_hist.png" alt="Histogram" height="250px">
						</div>
					</div>
				</div>
			</div>
		<button class="collapsible">
			<span style="position:relative;top:12px;"><font size="6">Twitter</font></span>
			<span style="position:relative;top:12px;" class="showHide"><font size="3">Sentiment Analysis</font></span>
			<a href="https://github.com/TalwalkarLab/leaf" style="float:right"><img src="webpage/images/twitter.png" alt="Logo" height="50"></a>
		</button>
		<div class="content">
			<div class="vertspace">
				<div class="infobox">
					<div class="infobox-1">
						<p>
							<b>Statistics</b><br>
							660,120 users<br>
							1,600,498 samples (total)<br>
							2.42 samples per user (mean)<br>
							num_samples (std): 4.71<br>
							num_samples (std/mean): 1.94<br>
						</p>
					</div>
					<div class="infobox-2">
						<img src="webpage/images/twitter_hist.png" alt="Histogram" height="250px">
					</div>
				</div>
			</div>
		</div>
		<button class="collapsible">
			<span style="position:relative;top:12px;"><font size="6">Celeba</font></span>
			<span style="position:relative;top:12px;" class="showHide"><font size="3">Image Classification</font></span>
			<a href="https://github.com/TalwalkarLab/leaf" style="float:right"><img src="webpage/images/celeba.png" alt="Logo" height="50"></a>
		</button>
		<div class="content">
			<div class="vertspace">
				<div class="infobox">
					<div class="infobox-1">
						<p>
							<b>Statistics</b><br>
							9,343 users<br>
							200,288 samples (total)<br>
							21.44 samples per user (mean)<br>
							num_samples (std): 7.63<br>
							num_samples (std/mean): 0.36<br>
						</p>
						<b>See the original <a href="http://mmlab.ie.cuhk.edu.hk/projects/CelebA.html" target="_blank" >Large-scale CelebFaces Attributes Dataset</a></b>
					</div>
					<div class="infobox-2">
						<img src="webpage/images/celeba_hist.png" alt="Histogram" height="250px">
					</div>
				</div>
			</div>
		</div>
		<button class="collapsible">
			<span style="position:relative;top:12px;"><font size="6">Synthetic Dataset</font></span>
			<span style="position:relative;top:12px;" class="showHide"><font size="3">Classification</font></span>
			<a href="https://github.com/TalwalkarLab/leaf" style="float:right"><img src="webpage/images/synthetic.png" alt="Logo" height="50"></a>
		</button>
		<div class="content">
			<div class="vertspace">
				<div class="infobox">
					<div class="infobox-1">
						<p>
							<b style="color:#FF5733">Default Distribution (parameters are customizable)</b><br>
							<b>Statistics</b><br>
							1,000 users<br>
							107,553 samples (total)<br>
							107.55 samples per user (mean)<br>
							num_samples (std): 213.22<br>
							num_samples (std/mean): 1.98<br>
						</p>
					</div>
					<div class="infobox-2">
						<img src="webpage/images/synthetic_hist.png" alt="Histogram" height="250px">
					</div>
				</div>
			</div>
		</div>
		<button class="collapsible">
			<span style="position:relative;top:12px;"><font size="6">Reddit</font></span>
			<span style="position:relative;top:12px;" class="showHide"><font size="3">Language Modeling</font></span>
			<a href="https://github.com/TalwalkarLab/leaf" style="float:right"><img src="webpage/images/reddit.png" alt="Logo" height="50"></a>
		</button>
		<div class="content">
			<div class="vertspace">
				<div class="infobox">
					<div class="infobox-1">
						<p>
							<b>Statistics</b><br>
							1,660,820 users<br>
							56,587,343 samples (total)<br>
							34.07 samples per user (mean)<br>
							num_samples (std): 62.9<br>
							num_samples (std/mean): 1.84<br>
						</p>
					</div>
					<div class="infobox-2">
						<img src="webpage/images/reddit_hist.png" alt="Histogram" height="250px">
					</div>
				</div>
			</div>
		</div>
	</div>

	<div class="title" style="position:relative;top:40px">
		<h2>Citation</h2>
        <span class="description">
            Please cite the following if you use LEAF: </br>
        </span>
        </br>
        <span class="citation" style="text-align: justify">
			<b><a href="https://arxiv.org/abs/1812.01097">LEAF: A Benchmark for Federated Settings</a></b></br>
			Sebastian Caldas, Sai Meher Karthik Duddu, Peter Wu, Tian Li, </br>
			Jakub Konečný, H. Brendan McMahan, Virginia Smith, and Ameet Talwalkar. </br> Workshop on Federated Learning for Data Privacy and Confidentiality (2019).
			<br/><br/>
        </span>
    </div>

	<script>
		var coll = document.getElementsByClassName("collapsible");
		var i;
		
		for (i = 0; i < coll.length; i++) {
			coll[i].addEventListener("click", function() {
                this.classList.toggle("active");
                var content = this.nextElementSibling;
                if (content.style.maxHeight){
                    content.style.maxHeight = null;
                } else {
                    content.style.maxHeight = "800px";
                } 
			});
		}
	</script>

</body>

</html>
