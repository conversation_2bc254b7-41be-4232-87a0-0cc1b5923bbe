import csv
import os
import sys
from collections import defaultdict

# 文件路径
file_path = 'models/metrics/metrics_stat.csv'

# 检查文件是否存在
if not os.path.exists(file_path):
    print(f"错误：文件 {file_path} 不存在")
    sys.exit(1)

print(f"正在分析文件: {file_path}")
print(f"文件大小: {os.path.getsize(file_path) / (1024*1024):.2f} MB")

# 收集每轮次的准确率和损失数据
round_data = defaultdict(lambda: {'test_acc': [], 'train_acc': [], 'test_loss': [], 'train_loss': []})

# 记录所有轮次
all_rounds = set()

print("\n读取文件头部来检查格式...")
with open(file_path, 'r') as f:
    reader = csv.reader(f)
    header = next(reader)  # 读取标题行
    print(f"文件列名: {header}")
    
    # 找到关键列的索引
    try:
        round_idx = header.index('round_number')
        acc_idx = header.index('accuracy')
        loss_idx = header.index('loss')
        set_idx = header.index('set')
    except ValueError as e:
        print(f"错误：找不到必要的列: {e}")
        sys.exit(1)

print("\n开始采样读取文件...")
# 设置采样间隔，只读取每个轮次的少量数据点
sample_every_n_lines = 100  # 每100行取1行
line_count = 0
sampled_count = 0
max_rounds = 2000  # 假设最大轮数不超过2000

with open(file_path, 'r') as f:
    reader = csv.reader(f)
    next(reader)  # 跳过标题行
    
    for row in reader:
        line_count += 1
        
        # 采样读取
        if line_count % sample_every_n_lines != 0:
            continue
        
        sampled_count += 1
        
        try:
            round_num = int(row[round_idx])
            accuracy = float(row[acc_idx])
            loss = float(row[loss_idx])
            data_set = row[set_idx]
            
            # 记录该轮次
            all_rounds.add(round_num)
            
            # 收集准确率和损失数据
            if data_set == 'test':
                round_data[round_num]['test_acc'].append(accuracy)
                round_data[round_num]['test_loss'].append(loss)
            elif data_set == 'train':
                round_data[round_num]['train_acc'].append(accuracy)
                round_data[round_num]['train_loss'].append(loss)
        except (IndexError, ValueError) as e:
            # 跳过有问题的行
            continue
        
        # 每处理1000个采样点显示一次进度
        if sampled_count % 1000 == 0:
            print(f"已采样处理 {sampled_count} 行...")

print(f"\n共处理 {line_count} 行数据，采样 {sampled_count} 行")
print(f"发现 {len(all_rounds)} 个不同的训练轮次")

# 计算每轮的平均准确率和损失
test_accuracies = {}
train_accuracies = {}
test_losses = {}
train_losses = {}

for round_num in sorted(all_rounds):
    # 计算测试集准确率
    if round_data[round_num]['test_acc']:
        test_acc = sum(round_data[round_num]['test_acc']) / len(round_data[round_num]['test_acc'])
        test_accuracies[round_num] = test_acc
    
    # 计算训练集准确率
    if round_data[round_num]['train_acc']:
        train_acc = sum(round_data[round_num]['train_acc']) / len(round_data[round_num]['train_acc'])
        train_accuracies[round_num] = train_acc
    
    # 计算测试集损失
    if round_data[round_num]['test_loss']:
        test_loss = sum(round_data[round_num]['test_loss']) / len(round_data[round_num]['test_loss'])
        test_losses[round_num] = test_loss
    
    # 计算训练集损失
    if round_data[round_num]['train_loss']:
        train_loss = sum(round_data[round_num]['train_loss']) / len(round_data[round_num]['train_loss'])
        train_losses[round_num] = train_loss

# 输出准确率和损失随轮数的变化
print("\n===== 准确率和损失随轮数的变化 =====")
print("轮数\t测试准确率\t训练准确率\t测试损失\t训练损失")

# 按轮数排序的数据
sorted_rounds = sorted(all_rounds)
sample_interval = max(1, len(sorted_rounds) // 20)  # 最多显示20个数据点

for i, r in enumerate(sorted_rounds):
    if i % sample_interval == 0 or i == len(sorted_rounds) - 1:
        test_acc = test_accuracies.get(r, "N/A")
        train_acc = train_accuracies.get(r, "N/A")
        test_loss = test_losses.get(r, "N/A")
        train_loss = train_losses.get(r, "N/A")
        
        if isinstance(test_acc, float):
            test_acc = f"{test_acc:.4f}"
        if isinstance(train_acc, float):
            train_acc = f"{train_acc:.4f}"
        if isinstance(test_loss, float):
            test_loss = f"{test_loss:.4f}"
        if isinstance(train_loss, float):
            train_loss = f"{train_loss:.4f}"
        
        print(f"{r}\t{test_acc}\t{train_acc}\t{test_loss}\t{train_loss}")

# 找出测试集准确率最高的轮次
if test_accuracies:
    max_acc_round = max(test_accuracies.keys(), key=lambda k: test_accuracies[k])
    max_accuracy = test_accuracies[max_acc_round]
    print(f"\n最高测试准确率: {max_accuracy:.4f} (轮数: {max_acc_round})")

    # 判断收敛点（使用简单方法：找到准确率达到最高值95%的第一个轮次）
    threshold = 0.95 * max_accuracy
    convergence_candidate = None
    
    for r in sorted_rounds:
        if r in test_accuracies and test_accuracies[r] >= threshold:
            convergence_candidate = r
            break
    
    if convergence_candidate:
        # 计算收敛后平均准确率
        post_convergence_accs = [test_accuracies[r] for r in sorted_rounds if r >= convergence_candidate and r in test_accuracies]
        avg_post_convergence = sum(post_convergence_accs) / len(post_convergence_accs)
        
        print(f"\n估计收敛轮数: {convergence_candidate}")
        print(f"收敛时准确率: {test_accuracies[convergence_candidate]:.4f}")
        print(f"收敛后平均准确率: {avg_post_convergence:.4f}")

# 保存数据到CSV文件以便后续可能的绘图
print("\n保存处理后的数据到CSV文件...")
with open('metrics_processed.csv', 'w', newline='') as f:
    writer = csv.writer(f)
    writer.writerow(['round', 'test_accuracy', 'train_accuracy', 'test_loss', 'train_loss'])
    
    for round_num in sorted_rounds:
        test_acc = test_accuracies.get(round_num, "")
        train_acc = train_accuracies.get(round_num, "")
        test_loss = test_losses.get(round_num, "")
        train_loss = train_losses.get(round_num, "")
        writer.writerow([round_num, test_acc, train_acc, test_loss, train_loss])

print("处理后的数据已保存为 metrics_processed.csv") 