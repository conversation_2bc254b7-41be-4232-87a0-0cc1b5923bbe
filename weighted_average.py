import csv
import matplotlib.pyplot as plt
import numpy as np

# 定义要分析的轮数
rounds = [0, 100, 200, 300, 400, 500, 600, 700, 800, 900, 1000, 1500, 2000]

# 存储结果的字典
weighted_results = {}
for round_num in rounds:
    weighted_results[round_num] = {
        'total_samples': 0,
        'weighted_accuracy_sum': 0,
        'weighted_loss_sum': 0
    }

# 读取CSV文件并计算加权平均
with open('models/metrics/metrics_2/metrics_stat.csv', 'r') as file:
    reader = csv.reader(file)
    header = next(reader)  # 跳过标题行
    
    for row in reader:
        # 解析行数据
        client_id = row[0]
        round_num = int(row[1])
        num_samples = int(row[3])
        set_type = row[4]
        accuracy = float(row[5])
        loss = float(row[6])
        
        # 只处理测试集数据和我们关注的轮数
        if set_type == 'test' and round_num in rounds:
            weighted_results[round_num]['total_samples'] += num_samples
            weighted_results[round_num]['weighted_accuracy_sum'] += accuracy * num_samples
            weighted_results[round_num]['weighted_loss_sum'] += loss * num_samples

# 计算加权平均值
weighted_accuracy = []
weighted_loss = []

for round_num in rounds:
    if weighted_results[round_num]['total_samples'] > 0:
        avg_accuracy = weighted_results[round_num]['weighted_accuracy_sum'] / weighted_results[round_num]['total_samples']
        avg_loss = weighted_results[round_num]['weighted_loss_sum'] / weighted_results[round_num]['total_samples']
    else:
        avg_accuracy = 0
        avg_loss = 0
    
    weighted_accuracy.append(avg_accuracy)
    weighted_loss.append(avg_loss)
    
    print(f"Round {round_num}:")
    print(f"  Weighted Average Accuracy: {avg_accuracy:.4f}")
    print(f"  Weighted Average Loss: {avg_loss:.4f}")
    print(f"  Total Samples: {weighted_results[round_num]['total_samples']}")
    print()

# 保存结果到CSV文件
with open('weighted_results.csv', 'w', newline='') as file:
    writer = csv.writer(file)
    writer.writerow(['Round', 'Weighted_Accuracy', 'Weighted_Loss', 'Total_Samples'])
    
    for i, round_num in enumerate(rounds):
        writer.writerow([
            round_num, 
            weighted_accuracy[i], 
            weighted_loss[i], 
            weighted_results[round_num]['total_samples']
        ])

# 创建图形和子图
fig, (ax1, ax2) = plt.subplots(2, 1, figsize=(10, 12))
fig.suptitle('模型收敛分析 (加权平均)', fontsize=16)

# 绘制准确率图
ax1.plot(rounds, weighted_accuracy, 'b-o', linewidth=2, markersize=8)
ax1.set_xlabel('轮数', fontsize=12)
ax1.set_ylabel('加权平均准确率', fontsize=12)
ax1.set_title('加权平均准确率随轮数的变化', fontsize=14)
ax1.grid(True)
ax1.set_xlim(0, 2100)
ax1.set_ylim(0, 1.0)

# 找到早期收敛点和最终收敛点
early_convergence_idx = 0
for i in range(1, len(rounds)):
    if i < len(rounds) - 1 and abs(weighted_accuracy[i] - weighted_accuracy[i+1]) < 0.03:
        early_convergence_idx = i
        break

early_round = rounds[early_convergence_idx]
early_accuracy = weighted_accuracy[early_convergence_idx]
final_round = rounds[-1]
final_accuracy = weighted_accuracy[-1]

# 添加收敛点标记
ax1.axvline(x=early_round, color='r', linestyle='--', alpha=0.7, label=f'早期收敛点 (轮数={early_round})')
ax1.axhline(y=early_accuracy, color='r', linestyle=':', alpha=0.7)
ax1.plot(early_round, early_accuracy, 'ro', markersize=10)
ax1.annotate(f'早期收敛点\n准确率: {early_accuracy:.4f}', xy=(early_round, early_accuracy), xytext=(early_round-200, early_accuracy-0.15),
            arrowprops=dict(facecolor='black', shrink=0.05, width=1.5, headwidth=8), fontsize=10)

ax1.axvline(x=final_round, color='g', linestyle='--', alpha=0.7, label=f'最终收敛点 (轮数={final_round})')
ax1.axhline(y=final_accuracy, color='g', linestyle=':', alpha=0.7)
ax1.plot(final_round, final_accuracy, 'go', markersize=10)
ax1.annotate(f'最终收敛点\n准确率: {final_accuracy:.4f}', xy=(final_round, final_accuracy), xytext=(final_round-300, final_accuracy+0.1),
            arrowprops=dict(facecolor='black', shrink=0.05, width=1.5, headwidth=8), fontsize=10)

# 标记三个阶段
ax1.axvspan(0, early_round, alpha=0.2, color='yellow', label='快速收敛阶段')
ax1.axvspan(early_round, 1500, alpha=0.2, color='orange', label='波动阶段')
ax1.axvspan(1500, 2000, alpha=0.2, color='lightgreen', label='再优化阶段')

ax1.legend(loc='lower right')

# 绘制损失图
ax2.plot(rounds, weighted_loss, 'r-o', linewidth=2, markersize=8)
ax2.set_xlabel('轮数', fontsize=12)
ax2.set_ylabel('加权平均损失', fontsize=12)
ax2.set_title('加权平均损失随轮数的变化', fontsize=14)
ax2.grid(True)
ax2.set_xlim(0, 2100)

early_loss = weighted_loss[early_convergence_idx]
final_loss = weighted_loss[-1]

# 添加收敛点标记
ax2.axvline(x=early_round, color='r', linestyle='--', alpha=0.7, label=f'早期收敛点 (轮数={early_round})')
ax2.axhline(y=early_loss, color='r', linestyle=':', alpha=0.7)
ax2.plot(early_round, early_loss, 'ro', markersize=10)
ax2.annotate(f'早期收敛点\n损失: {early_loss:.4f}', xy=(early_round, early_loss), xytext=(early_round-200, early_loss+0.5),
            arrowprops=dict(facecolor='black', shrink=0.05, width=1.5, headwidth=8), fontsize=10)

ax2.axvline(x=final_round, color='g', linestyle='--', alpha=0.7, label=f'最终收敛点 (轮数={final_round})')
ax2.axhline(y=final_loss, color='g', linestyle=':', alpha=0.7)
ax2.plot(final_round, final_loss, 'go', markersize=10)
ax2.annotate(f'最终收敛点\n损失: {final_loss:.4f}', xy=(final_round, final_loss), xytext=(final_round-300, final_loss-0.5),
            arrowprops=dict(facecolor='black', shrink=0.05, width=1.5, headwidth=8), fontsize=10)

# 标记三个阶段
ax2.axvspan(0, early_round, alpha=0.2, color='yellow')
ax2.axvspan(early_round, 1500, alpha=0.2, color='orange')
ax2.axvspan(1500, 2000, alpha=0.2, color='lightgreen')

ax2.legend(loc='upper right')

# 调整布局并保存
plt.tight_layout(rect=[0, 0, 1, 0.96])
plt.savefig('weighted_convergence_analysis.png', dpi=300, bbox_inches='tight')
plt.close()

# 创建第二个图：准确率和损失在同一图中
plt.figure(figsize=(12, 8))
plt.title('加权平均准确率和损失随轮数的变化', fontsize=16)

# 创建两个Y轴
ax1 = plt.gca()
ax2 = ax1.twinx()

# 绘制准确率
line1, = ax1.plot(rounds, weighted_accuracy, 'b-o', linewidth=2, markersize=8, label='加权平均准确率')
ax1.set_xlabel('轮数', fontsize=14)
ax1.set_ylabel('加权平均准确率', color='b', fontsize=14)
ax1.tick_params(axis='y', labelcolor='b')
ax1.set_xlim(0, 2100)
ax1.set_ylim(0, 1.0)
ax1.grid(True, alpha=0.3)

# 绘制损失
line2, = ax2.plot(rounds, weighted_loss, 'r-^', linewidth=2, markersize=8, label='加权平均损失')
ax2.set_ylabel('加权平均损失', color='r', fontsize=14)
ax2.tick_params(axis='y', labelcolor='r')
ax2.set_ylim(0, 4.5)

# 添加收敛点标记
ax1.axvline(x=early_round, color='purple', linestyle='--', alpha=0.7, label=f'早期收敛点 (轮数={early_round})')
ax1.axvline(x=final_round, color='green', linestyle='--', alpha=0.7, label=f'最终收敛点 (轮数={final_round})')

# 标记三个阶段
plt.axvspan(0, early_round, alpha=0.2, color='yellow', label='快速收敛阶段')
plt.axvspan(early_round, 1500, alpha=0.2, color='orange', label='波动阶段')
plt.axvspan(1500, 2000, alpha=0.2, color='lightgreen', label='再优化阶段')

# 合并图例
lines = [line1, line2]
labels = [l.get_label() for l in lines]
ax1.legend(lines, labels, loc='center right', fontsize=12)

# 添加网格线
plt.grid(True, alpha=0.3)

# 保存图表
plt.tight_layout()
plt.savefig('weighted_convergence_combined.png', dpi=300, bbox_inches='tight')
plt.close()

print("图表已生成: weighted_convergence_analysis.png 和 weighted_convergence_combined.png")
