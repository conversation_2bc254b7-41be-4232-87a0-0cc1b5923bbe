import csv
import os
import matplotlib.pyplot as plt
from collections import defaultdict

# File path
file_path = 'models/metrics/metrics_stat.csv'

# Check if file exists
if not os.path.exists(file_path):
    print(f"Error: File {file_path} does not exist")
    exit(1)

print(f"Analyzing file: {file_path}")
print(f"File size: {os.path.getsize(file_path) / (1024*1024):.2f} MB")

# Collect accuracy and loss data for each round
round_data = defaultdict(lambda: {'test_acc': [], 'train_acc': [], 'test_loss': [], 'train_loss': []})

# Record all rounds
all_rounds = set()

print("\nReading file header to check format...")
with open(file_path, 'r') as f:
    reader = csv.reader(f)
    header = next(reader)  # Read header row
    print(f"File columns: {header}")
    
    # Find indices of key columns
    try:
        round_idx = header.index('round_number')
        acc_idx = header.index('accuracy')
        loss_idx = header.index('loss')
        set_idx = header.index('set')
    except ValueError as e:
        print(f"Error: Cannot find necessary columns: {e}")
        exit(1)

print("\nStarting to read and analyze the entire file...")
# Set a counter to show progress
line_count = 0
last_reported = 0

with open(file_path, 'r') as f:
    reader = csv.reader(f)
    next(reader)  # Skip header row
    
    for row in reader:
        line_count += 1
        
        # Show progress every 100,000 lines
        if line_count % 100000 == 0 and line_count != last_reported:
            print(f"Processed {line_count} lines...")
            last_reported = line_count
        
        try:
            round_num = int(row[round_idx])
            accuracy = float(row[acc_idx])
            loss = float(row[loss_idx])
            data_set = row[set_idx]
            
            # Record this round
            all_rounds.add(round_num)
            
            # Collect accuracy and loss data
            if data_set == 'test':
                round_data[round_num]['test_acc'].append(accuracy)
                round_data[round_num]['test_loss'].append(loss)
            elif data_set == 'train':
                round_data[round_num]['train_acc'].append(accuracy)
                round_data[round_num]['train_loss'].append(loss)
        except (IndexError, ValueError) as e:
            # Skip problematic rows
            continue

print(f"\nProcessed {line_count} lines of data")
print(f"Found {len(all_rounds)} different training rounds")

# Calculate average accuracy and loss for each round
rounds = sorted(all_rounds)
test_accuracies = []
train_accuracies = []
test_losses = []
train_losses = []
round_numbers = []

for round_num in rounds:
    round_numbers.append(round_num)
    
    # Calculate test set accuracy
    if round_data[round_num]['test_acc']:
        test_acc = sum(round_data[round_num]['test_acc']) / len(round_data[round_num]['test_acc'])
        test_accuracies.append(test_acc)
    else:
        test_accuracies.append(None)
    
    # Calculate training set accuracy
    if round_data[round_num]['train_acc']:
        train_acc = sum(round_data[round_num]['train_acc']) / len(round_data[round_num]['train_acc'])
        train_accuracies.append(train_acc)
    else:
        train_accuracies.append(None)
    
    # Calculate test set loss
    if round_data[round_num]['test_loss']:
        test_loss = sum(round_data[round_num]['test_loss']) / len(round_data[round_num]['test_loss'])
        test_losses.append(test_loss)
    else:
        test_losses.append(None)
    
    # Calculate training set loss
    if round_data[round_num]['train_loss']:
        train_loss = sum(round_data[round_num]['train_loss']) / len(round_data[round_num]['train_loss'])
        train_losses.append(train_loss)
    else:
        train_losses.append(None)

# Remove None values, prepare plotting data
plot_rounds = []
plot_test_acc = []
plot_train_acc = []
plot_test_loss = []
plot_train_loss = []

for i, r in enumerate(round_numbers):
    # Only add rounds with data for both test and training sets
    if test_accuracies[i] is not None and train_accuracies[i] is not None:
        plot_rounds.append(r)
        plot_test_acc.append(test_accuracies[i])
        plot_train_acc.append(train_accuracies[i])
        plot_test_loss.append(test_losses[i])
        plot_train_loss.append(train_losses[i])

# Find the round with highest test accuracy
if plot_test_acc:
    max_acc_idx = plot_test_acc.index(max(plot_test_acc))
    max_acc_round = plot_rounds[max_acc_idx]
    max_accuracy = plot_test_acc[max_acc_idx]
    print(f"\nHighest test accuracy: {max_accuracy:.4f} (Round: {max_acc_round})")

    # Simple method to determine convergence: find the first round where accuracy reaches 95% of the maximum
    threshold = 0.95 * max_accuracy
    convergence_idx = None
    
    for i, acc in enumerate(plot_test_acc):
        if acc >= threshold:
            convergence_idx = i
            break
    
    if convergence_idx is not None:
        convergence_round = plot_rounds[convergence_idx]
        convergence_acc = plot_test_acc[convergence_idx]
        print(f"Estimated convergence round: {convergence_round}")
        print(f"Accuracy at convergence: {convergence_acc:.4f}")

# Plot accuracy graph
plt.figure(figsize=(12, 6))
plt.plot(plot_rounds, plot_test_acc, 'b-', label='Test Set Accuracy')
plt.plot(plot_rounds, plot_train_acc, 'r-', label='Training Set Accuracy')

if convergence_idx is not None:
    plt.axvline(x=convergence_round, color='g', linestyle='--', label=f'Convergence Round: {convergence_round}')

plt.title('Accuracy vs Training Rounds')
plt.xlabel('Training Rounds')
plt.ylabel('Accuracy')
plt.legend()
plt.grid(True)
plt.savefig('accuracy_vs_rounds.png', dpi=300)
print("Accuracy graph saved as accuracy_vs_rounds.png")

# Plot loss graph
plt.figure(figsize=(12, 6))
plt.plot(plot_rounds, plot_test_loss, 'b-', label='Test Set Loss')
plt.plot(plot_rounds, plot_train_loss, 'r-', label='Training Set Loss')

if convergence_idx is not None:
    plt.axvline(x=convergence_round, color='g', linestyle='--', label=f'Convergence Round: {convergence_round}')

plt.title('Loss vs Training Rounds')
plt.xlabel('Training Rounds')
plt.ylabel('Loss')
plt.legend()
plt.grid(True)
plt.savefig('loss_vs_rounds.png', dpi=300)
print("Loss graph saved as loss_vs_rounds.png")

# If there are too many rounds, sample points for a clearer plot
if len(plot_rounds) > 50:
    # Take every Nth point
    sample_interval = len(plot_rounds) // 50 + 1
    sampled_indices = list(range(0, len(plot_rounds), sample_interval))
    
    # Ensure to include start, end, and max accuracy points
    if max_acc_idx not in sampled_indices:
        sampled_indices.append(max_acc_idx)
    if convergence_idx is not None and convergence_idx not in sampled_indices:
        sampled_indices.append(convergence_idx)
    sampled_indices = sorted(sampled_indices)
    
    sampled_rounds = [plot_rounds[i] for i in sampled_indices]
    sampled_test_acc = [plot_test_acc[i] for i in sampled_indices]
    sampled_train_acc = [plot_train_acc[i] for i in sampled_indices]
    sampled_test_loss = [plot_test_loss[i] for i in sampled_indices]
    sampled_train_loss = [plot_train_loss[i] for i in sampled_indices]
    
    # Plot sampled accuracy graph
    plt.figure(figsize=(12, 6))
    plt.plot(sampled_rounds, sampled_test_acc, 'bo-', label='Test Set Accuracy')
    plt.plot(sampled_rounds, sampled_train_acc, 'ro-', label='Training Set Accuracy')
    
    if convergence_idx is not None:
        plt.axvline(x=convergence_round, color='g', linestyle='--', label=f'Convergence Round: {convergence_round}')
    
    plt.title('Accuracy vs Training Rounds (Sampled)')
    plt.xlabel('Training Rounds')
    plt.ylabel('Accuracy')
    plt.legend()
    plt.grid(True)
    plt.savefig('accuracy_vs_rounds_sampled.png', dpi=300)
    print("Sampled accuracy graph saved as accuracy_vs_rounds_sampled.png")
    
    # Plot sampled loss graph
    plt.figure(figsize=(12, 6))
    plt.plot(sampled_rounds, sampled_test_loss, 'bo-', label='Test Set Loss')
    plt.plot(sampled_rounds, sampled_train_loss, 'ro-', label='Training Set Loss')
    
    if convergence_idx is not None:
        plt.axvline(x=convergence_round, color='g', linestyle='--', label=f'Convergence Round: {convergence_round}')
    
    plt.title('Loss vs Training Rounds (Sampled)')
    plt.xlabel('Training Rounds')
    plt.ylabel('Loss')
    plt.legend()
    plt.grid(True)
    plt.savefig('loss_vs_rounds_sampled.png', dpi=300)
    print("Sampled loss graph saved as loss_vs_rounds_sampled.png")

print("\nAnalysis and plotting completed.")