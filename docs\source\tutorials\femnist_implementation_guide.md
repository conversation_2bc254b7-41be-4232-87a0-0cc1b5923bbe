# FEMNIST Dataset Implementation Guide

This guide covers key aspects of working with the FEMNIST dataset in the LEAF framework, including data preprocessing, model implementation, training, and evaluation.

## 1. Data Structure and Preprocessing

The FEMNIST dataset is a federated version of the EMNIST dataset, where data is partitioned by writers of the digits/characters. This creates a natural non-IID distribution of the data.

### Data Preprocessing Steps:

1. **Fetch and preprocess the data:**
   ```bash
   cd private-ai/leaf/data/femnist/
   ./preprocess.sh -s niid --sf 0.05 -k 0 -t sample --smplseed 1549786595 --spltseed 1549786796
   ```
   
   Parameters explained:
   - `-s niid`: Use non-IID data partitioning
   - `--sf 0.05`: Sample 5% of the dataset 
   - `-k 0`: Keep all clients (k=0 means no subsampling of clients)
   - `-t sample`: Use sampling to create train/test sets
   - `--smplseed 1549786595`: Seed for sampling reproducibility
   - `--spltseed 1549786796`: Seed for splitting reproducibility

2. **Processed data location:**
   After preprocessing, the data will be available in:
   - `data/femnist/data/train/` - Training data
   - `data/femnist/data/test/` - Testing data

### Data Format:

The data is stored in a hierarchical structure representing the federated nature:
- Each client (writer) has their own data split
- Data is stored in JSON format with images flattened to 1D arrays
- The framework's `read_data` function handles loading this structure

## 2. CNN Model Architecture

The FEMNIST model is a convolutional neural network defined in `models/femnist/cnn.py`. Key components:

```python
# Model structure
input_layer = tf.reshape(features, [-1, IMAGE_SIZE, IMAGE_SIZE, 1])  # 28x28 images
conv1 = tf.layers.conv2d(inputs=input_layer, filters=32, kernel_size=[5, 5], padding="same", activation=tf.nn.relu)
pool1 = tf.layers.max_pooling2d(inputs=conv1, pool_size=[2, 2], strides=2)
conv2 = tf.layers.conv2d(inputs=pool1, filters=64, kernel_size=[5, 5], padding="same", activation=tf.nn.relu)
pool2 = tf.layers.max_pooling2d(inputs=conv2, pool_size=[2, 2], strides=2)
pool2_flat = tf.reshape(pool2, [-1, 7 * 7 * 64])
dense = tf.layers.dense(inputs=pool2_flat, units=2048, activation=tf.nn.relu)
logits = tf.layers.dense(inputs=dense, units=self.num_classes)
```

Key aspects of the model:
- Two convolutional layers with 5x5 filters
- Max pooling after each convolutional layer
- A dense layer with 2048 units
- Output layer with units equal to the number of classes (62 for FEMNIST - digits and characters)

## 3. Training Methods

The framework supports two key training methods:

### Minibatch SGD:

```bash
python main.py -dataset femnist -model cnn -lr 0.06 --minibatch 0.1 --clients-per-round 3 --num-rounds 2000
```

Parameters:
- `-lr 0.06`: Learning rate
- `--minibatch 0.1`: Use 10% batch size
- `--clients-per-round 3`: Train with 3 clients per round
- `--num-rounds 2000`: Train for 2000 communication rounds

### FedAvg (Federated Averaging):

```bash
python main.py -dataset femnist -model cnn -lr 0.004 --clients-per-round 3 --num-epochs 1 --num-rounds 2000
```

Additional parameters:
- `--num-epochs`: Number of local epochs per client (default is 1)

## 4. Metrics and Evaluation

### Available Metrics:

The framework collects two types of metrics:
- **Statistical metrics** (`stat_metrics.csv`): Accuracy, loss
- **System metrics** (`sys_metrics.csv`): Computation time, bytes transmitted

### Evaluation:

Model performance is evaluated after specified rounds (default is every 10 rounds). During evaluation:
1. The global model is tested on all clients' local test data
2. Results are aggregated and reported as weighted averages and percentiles

## 5. Analyzing and Extending the Implementation

### Comparing Different Methods:

Use the provided script to compare different configurations:
```bash
cd private-ai/leaf/paper_experiments
./femnist.sh ./results
```

This script automatically runs both minibatch SGD and FedAvg with various configurations.

### Extending the Model:

To modify the model architecture:
1. Edit `models/femnist/cnn.py` to change layers, hyperparameters, etc.
2. Consider changes like:
   - Adding batch normalization
   - Using different activation functions
   - Adding dropout for regularization
   - Testing different optimizers

Example of adding dropout:
```python
dense = tf.layers.dense(inputs=pool2_flat, units=2048, activation=tf.nn.relu)
dropout = tf.layers.dropout(inputs=dense, rate=0.5, training=mode == tf.estimator.ModeKeys.TRAIN)
logits = tf.layers.dense(inputs=dropout, units=self.num_classes)
```

### Custom Client Selection:

By default, clients are randomly selected each round. To implement custom selection:
1. Modify the `online` function in `main.py`
2. Implement alternative selection strategies based on client properties, history, etc.

## 6. Troubleshooting

### Common Issues:

1. **Out of Memory Errors**:
   - Decrease batch size
   - Reduce model complexity
   - Lower the number of clients per round

2. **Slow Convergence**:
   - Adjust learning rate
   - Experiment with different client selection strategies
   - Modify local epoch counts for FedAvg

3. **Performance Disparities**:
   - The non-IID nature of the data may cause performance disparities
   - Monitor percentile metrics to identify outlier clients
   - Consider implementing techniques like client weighting or proximal term regularization

### Debug Tips:

- Use `tf.debugging.set_log_device_placement(True)` for device placement debugging
- Enable TensorFlow debugging: `tf.logging.set_verbosity(tf.logging.INFO)`
- Add logging to track model state and gradients during training

## 7. Performance Optimization

For better performance, consider:

1. **Parallel client computation**:
   - Modify `server.py` to process client updates in parallel

2. **Model compression**:
   - Implement gradient quantization
   - Use model pruning techniques

3. **Adaptive learning rates**:
   - Implement client-specific learning rates based on data properties
   - Consider using adaptive optimizers like Adam instead of SGD

## 8. Resources

- [LEAF Paper](https://arxiv.org/abs/1812.01097)
- [TensorFlow Documentation](https://www.tensorflow.org/versions/r1.15/api_docs/)
- [Federated Learning Systems: Vision and Challenges](https://dl.acm.org/doi/10.1145/3572192)
