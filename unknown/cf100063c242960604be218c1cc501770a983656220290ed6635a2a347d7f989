import matplotlib.pyplot as plt
import numpy as np
import pandas as pd

# Data for weighted average accuracy
weighted_data = pd.read_csv('weighted_results.csv')

# Data for simple average accuracy (from our previous analysis)
rounds = [0, 100, 200, 300, 400, 500, 600, 700, 800, 900, 1000, 1500, 2000]
simple_accuracy = [0.0112, 0.2272, 0.4643, 0.5373, 0.6233, 0.6667, 0.7145, 0.7426, 0.7267, 0.7441, 0.7165, 0.6996, 0.7794]

# Create the plot
plt.figure(figsize=(12, 8))
plt.plot(weighted_data['Round'], weighted_data['Weighted_Accuracy'], 'b-o', linewidth=2, markersize=8, label='Weighted Average Accuracy')
plt.plot(rounds, simple_accuracy, 'r-^', linewidth=2, markersize=8, label='Simple Average Accuracy')

# Add labels and title
plt.xlabel('Number of Rounds', fontsize=14)
plt.ylabel('Accuracy', fontsize=14)
plt.title('Weighted vs. Simple Average Accuracy Comparison', fontsize=16)

# Add grid and legend
plt.grid(True, alpha=0.3)
plt.legend(fontsize=12)

# Mark the key points
# Early convergence point (round 700)
plt.axvline(x=700, color='purple', linestyle='--', alpha=0.7, label='Early Convergence (Round 700)')

# Final point (round 2000)
plt.axvline(x=2000, color='green', linestyle='--', alpha=0.7, label='Final Point (Round 2000)')

# Highlight the three phases
plt.axvspan(0, 700, alpha=0.1, color='yellow', label='Fast Convergence Phase')
plt.axvspan(700, 1500, alpha=0.1, color='orange', label='Fluctuation Phase')
plt.axvspan(1500, 2000, alpha=0.1, color='lightgreen', label='Re-optimization Phase')

# Set axis limits
plt.xlim(0, 2100)
plt.ylim(0, 0.85)

# Save the figure
plt.tight_layout()
plt.savefig('accuracy_comparison_simple.png', dpi=300)
plt.close()

print("Comparison plot generated: accuracy_comparison_simple.png")
