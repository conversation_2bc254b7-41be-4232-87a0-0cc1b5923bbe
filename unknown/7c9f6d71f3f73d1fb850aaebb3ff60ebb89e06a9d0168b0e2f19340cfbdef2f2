import csv
import os
import sys
from collections import defaultdict

# 文件路径
file_path = 'models/metrics/metrics_stat.csv'

# 检查文件是否存在
if not os.path.exists(file_path):
    print(f"错误：文件 {file_path} 不存在")
    sys.exit(1)

print(f"正在分析文件: {file_path}")
print(f"文件大小: {os.path.getsize(file_path) / (1024*1024):.2f} MB")

# 读取CSV文件并提取信息
try:
    # 收集每轮次的准确率数据
    round_accuracies = defaultdict(lambda: {'test': [], 'train': []})
    
    # 记录所有轮次
    all_rounds = set()
    
    # 仅读取前50行检查文件格式
    print("\n读取文件头部来检查格式...")
    with open(file_path, 'r') as f:
        reader = csv.reader(f)
        header = next(reader)  # 读取标题行
        print(f"文件列名: {header}")
        
        # 找到关键列的索引
        try:
            round_idx = header.index('round_number')
            acc_idx = header.index('accuracy')
            set_idx = header.index('set')
            
            # 读取几行看看
            for i, row in enumerate(reader):
                if i >= 10:  # 只读10行
                    break
                print(f"样本行 {i+1}: {row}")
                
        except ValueError as e:
            print(f"错误：找不到必要的列: {e}")
            sys.exit(1)
    
    print("\n开始逐行读取并分析整个文件...")
    # 设置一个计数器来显示进度
    line_count = 0
    last_reported = 0
    
    with open(file_path, 'r') as f:
        reader = csv.reader(f)
        next(reader)  # 跳过标题行
        
        for row in reader:
            line_count += 1
            
            # 每处理100,000行显示一次进度
            if line_count % 100000 == 0 and line_count != last_reported:
                print(f"已处理 {line_count} 行...")
                last_reported = line_count
            
            try:
                round_num = int(row[round_idx])
                accuracy = float(row[acc_idx])
                data_set = row[set_idx]
                
                # 记录该轮次
                all_rounds.add(round_num)
                
                # 收集准确率数据
                if data_set in ['test', 'train']:
                    round_accuracies[round_num][data_set].append(accuracy)
            except (IndexError, ValueError) as e:
                # 跳过有问题的行
                continue
    
    print(f"\n共处理 {line_count} 行数据")
    print(f"发现 {len(all_rounds)} 个不同的训练轮次")
    
    # 计算每轮的平均准确率
    test_accuracies = {}
    train_accuracies = {}
    
    for round_num in sorted(all_rounds):
        # 计算测试集准确率
        if round_accuracies[round_num]['test']:
            test_acc = sum(round_accuracies[round_num]['test']) / len(round_accuracies[round_num]['test'])
            test_accuracies[round_num] = test_acc
        
        # 计算训练集准确率
        if round_accuracies[round_num]['train']:
            train_acc = sum(round_accuracies[round_num]['train']) / len(round_accuracies[round_num]['train'])
            train_accuracies[round_num] = train_acc
    
    # 找出测试集准确率最高的轮次
    if test_accuracies:
        max_acc_round = max(test_accuracies.keys(), key=lambda k: test_accuracies[k])
        max_accuracy = test_accuracies[max_acc_round]
        print(f"\n最高测试准确率: {max_accuracy:.4f} (轮数: {max_acc_round})")
    
    # 判断收敛点（使用简单方法：找到准确率达到最高值95%且之后变化小的点）
    if test_accuracies:
        # 排序轮次
        sorted_rounds = sorted(test_accuracies.keys())
        
        # 找到准确率达到最大值95%的轮次
        threshold = 0.95 * max_accuracy
        convergence_candidates = []
        
        for r in sorted_rounds:
            if test_accuracies[r] >= threshold:
                convergence_candidates.append(r)
        
        if convergence_candidates:
            # 选择达到阈值的第一个轮次作为收敛点
            convergence_round = convergence_candidates[0]
            
            # 计算收敛后平均准确率
            post_convergence_accs = [test_accuracies[r] for r in sorted_rounds if r >= convergence_round]
            if post_convergence_accs:
                avg_post_convergence = sum(post_convergence_accs) / len(post_convergence_accs)
                
                print(f"\n估计收敛轮数: {convergence_round}")
                print(f"收敛时准确率: {test_accuracies[convergence_round]:.4f}")
                print(f"收敛后平均准确率: {avg_post_convergence:.4f}")
    
    # 输出部分轮次的准确率，以查看变化趋势
    print("\n测试准确率数据采样:")
    sample_interval = max(1, len(sorted_rounds) // 20)  # 最多显示20个数据点
    for i, r in enumerate(sorted_rounds):
        if i % sample_interval == 0 or r == max_acc_round:
            print(f"轮数 {r}: {test_accuracies[r]:.4f}")
    
except Exception as e:
    print(f"处理过程中出错: {str(e)}")
    import traceback
    traceback.print_exc() 