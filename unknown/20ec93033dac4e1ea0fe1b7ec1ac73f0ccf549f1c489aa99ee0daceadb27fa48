import csv
import matplotlib.pyplot as plt
import numpy as np
import pandas as pd

# 定义要分析的轮数
rounds = [0, 100, 200, 300, 400, 500, 600, 700, 800, 900, 1000, 1500, 2000]

# 存储结果的字典
weighted_results = {}
for round_num in rounds:
    weighted_results[round_num] = {
        'train': {'total_samples': 0, 'weighted_accuracy_sum': 0},
        'test': {'total_samples': 0, 'weighted_accuracy_sum': 0}
    }

# 读取CSV文件并计算加权平均
with open('models/metrics/metrics_2/metrics_stat.csv', 'r') as file:
    reader = csv.reader(file)
    header = next(reader)  # 跳过标题行
    
    for row in reader:
        # 解析行数据
        client_id = row[0]
        round_num = int(row[1])
        num_samples = int(row[3])
        set_type = row[4]  # 'train' 或 'test'
        accuracy = float(row[5])
        
        # 只处理我们关注的轮数
        if round_num in rounds and set_type in ['train', 'test']:
            weighted_results[round_num][set_type]['total_samples'] += num_samples
            weighted_results[round_num][set_type]['weighted_accuracy_sum'] += accuracy * num_samples

# 计算加权平均值
train_weighted_accuracy = []
test_weighted_accuracy = []
train_samples = []
test_samples = []

for round_num in rounds:
    # 计算训练集加权平均准确率
    if weighted_results[round_num]['train']['total_samples'] > 0:
        train_avg = weighted_results[round_num]['train']['weighted_accuracy_sum'] / weighted_results[round_num]['train']['total_samples']
    else:
        train_avg = 0
    train_weighted_accuracy.append(train_avg)
    train_samples.append(weighted_results[round_num]['train']['total_samples'])
    
    # 计算测试集加权平均准确率
    if weighted_results[round_num]['test']['total_samples'] > 0:
        test_avg = weighted_results[round_num]['test']['weighted_accuracy_sum'] / weighted_results[round_num]['test']['total_samples']
    else:
        test_avg = 0
    test_weighted_accuracy.append(test_avg)
    test_samples.append(weighted_results[round_num]['test']['total_samples'])
    
    print(f"Round {round_num}:")
    print(f"  Train Weighted Average Accuracy: {train_avg:.4f} (Samples: {weighted_results[round_num]['train']['total_samples']})")
    print(f"  Test Weighted Average Accuracy: {test_avg:.4f} (Samples: {weighted_results[round_num]['test']['total_samples']})")
    print()

# 保存结果到CSV文件
with open('train_test_weighted_results.csv', 'w', newline='') as file:
    writer = csv.writer(file)
    writer.writerow(['Round', 'Train_Weighted_Accuracy', 'Train_Samples', 'Test_Weighted_Accuracy', 'Test_Samples'])
    
    for i, round_num in enumerate(rounds):
        writer.writerow([
            round_num, 
            train_weighted_accuracy[i], 
            train_samples[i],
            test_weighted_accuracy[i], 
            test_samples[i]
        ])

# 创建训练集和测试集准确率对比图
plt.figure(figsize=(12, 8))
plt.plot(rounds, train_weighted_accuracy, 'b-o', linewidth=2, markersize=8, label='训练集加权平均准确率')
plt.plot(rounds, test_weighted_accuracy, 'r-^', linewidth=2, markersize=8, label='测试集加权平均准确率')

# 添加标签和标题
plt.xlabel('轮数', fontsize=14)
plt.ylabel('加权平均准确率', fontsize=14)
plt.title('训练集与测试集加权平均准确率对比', fontsize=16)

# 添加网格和图例
plt.grid(True, alpha=0.3)
plt.legend(fontsize=12)

# 标记关键点
# 早期收敛点（第700轮）
plt.axvline(x=700, color='purple', linestyle='--', alpha=0.7, label='早期收敛点 (轮数=700)')
plt.plot(700, train_weighted_accuracy[rounds.index(700)], 'bo', markersize=10)
plt.plot(700, test_weighted_accuracy[rounds.index(700)], 'ro', markersize=10)

# 最终点（第2000轮）
plt.axvline(x=2000, color='green', linestyle='--', alpha=0.7, label='最终点 (轮数=2000)')
plt.plot(2000, train_weighted_accuracy[rounds.index(2000)], 'bo', markersize=10)
plt.plot(2000, test_weighted_accuracy[rounds.index(2000)], 'ro', markersize=10)

# 计算训练集和测试集准确率的差异（过拟合指标）
train_test_diff = [train - test for train, test in zip(train_weighted_accuracy, test_weighted_accuracy)]
max_diff_idx = train_test_diff.index(max(train_test_diff))
max_diff_round = rounds[max_diff_idx]

# 标记训练集和测试集差异最大的点
plt.plot(max_diff_round, train_weighted_accuracy[max_diff_idx], 'bx', markersize=12, mew=3)
plt.plot(max_diff_round, test_weighted_accuracy[max_diff_idx], 'rx', markersize=12, mew=3)
plt.annotate(f'最大差异点\n轮数: {max_diff_round}\n差异: {train_test_diff[max_diff_idx]:.4f}', 
             xy=(max_diff_round, (train_weighted_accuracy[max_diff_idx] + test_weighted_accuracy[max_diff_idx])/2), 
             xytext=(max_diff_round+200, (train_weighted_accuracy[max_diff_idx] + test_weighted_accuracy[max_diff_idx])/2 - 0.1),
             arrowprops=dict(facecolor='black', shrink=0.05, width=1.5, headwidth=8), 
             fontsize=10)

# 标记三个阶段
plt.axvspan(0, 700, alpha=0.1, color='yellow', label='快速收敛阶段')
plt.axvspan(700, 1500, alpha=0.1, color='orange', label='波动阶段')
plt.axvspan(1500, 2000, alpha=0.1, color='lightgreen', label='再优化阶段')

# 设置坐标轴范围
plt.xlim(0, 2100)
plt.ylim(0, 1.0)

# 添加文本框，说明关键观察结果
textstr = '\n'.join((
    '关键观察:',
    f'1. 早期收敛点 (轮数=700):',
    f'   训练集准确率: {train_weighted_accuracy[rounds.index(700)]:.4f}',
    f'   测试集准确率: {test_weighted_accuracy[rounds.index(700)]:.4f}',
    f'   差异: {train_weighted_accuracy[rounds.index(700)] - test_weighted_accuracy[rounds.index(700)]:.4f}',
    f'2. 最终点 (轮数=2000):',
    f'   训练集准确率: {train_weighted_accuracy[rounds.index(2000)]:.4f}',
    f'   测试集准确率: {test_weighted_accuracy[rounds.index(2000)]:.4f}',
    f'   差异: {train_weighted_accuracy[rounds.index(2000)] - test_weighted_accuracy[rounds.index(2000)]:.4f}',
    f'3. 最大差异点 (轮数={max_diff_round}):',
    f'   训练集准确率: {train_weighted_accuracy[max_diff_idx]:.4f}',
    f'   测试集准确率: {test_weighted_accuracy[max_diff_idx]:.4f}',
    f'   差异: {train_test_diff[max_diff_idx]:.4f}'
))

props = dict(boxstyle='round', facecolor='wheat', alpha=0.5)
plt.text(1050, 0.2, textstr, fontsize=10, verticalalignment='bottom', bbox=props)

# 保存图表
plt.tight_layout()
plt.savefig('train_test_accuracy_comparison.png', dpi=300, bbox_inches='tight')
plt.close()

# 创建第二个图：训练-测试差异随轮数的变化
plt.figure(figsize=(12, 6))
plt.plot(rounds, train_test_diff, 'g-o', linewidth=2, markersize=8)

# 添加标签和标题
plt.xlabel('轮数', fontsize=14)
plt.ylabel('训练集-测试集准确率差异', fontsize=14)
plt.title('训练集与测试集准确率差异随轮数的变化（过拟合指标）', fontsize=16)

# 添加网格
plt.grid(True, alpha=0.3)

# 标记关键点
plt.axvline(x=700, color='purple', linestyle='--', alpha=0.7, label='早期收敛点 (轮数=700)')
plt.axvline(x=2000, color='green', linestyle='--', alpha=0.7, label='最终点 (轮数=2000)')
plt.axvline(x=max_diff_round, color='red', linestyle='--', alpha=0.7, label=f'最大差异点 (轮数={max_diff_round})')

# 标记三个阶段
plt.axvspan(0, 700, alpha=0.1, color='yellow', label='快速收敛阶段')
plt.axvspan(700, 1500, alpha=0.1, color='orange', label='波动阶段')
plt.axvspan(1500, 2000, alpha=0.1, color='lightgreen', label='再优化阶段')

# 添加零线（理想情况下训练集和测试集性能相同）
plt.axhline(y=0, color='black', linestyle='-', alpha=0.3)

# 设置坐标轴范围
plt.xlim(0, 2100)

# 添加图例
plt.legend(fontsize=12)

# 保存图表
plt.tight_layout()
plt.savefig('train_test_difference.png', dpi=300, bbox_inches='tight')
plt.close()

print("图表已生成: train_test_accuracy_comparison.png 和 train_test_difference.png")
