import pandas as pd
import numpy as np

try:
    # 尝试读取前1000行来分析文件结构
    print("尝试读取CSV文件前1000行...")
    file_path = 'models/metrics/metrics_stat.csv'
    sample_df = pd.read_csv(file_path, nrows=1000)
    
    # 显示文件基本信息
    print("\n文件列名:", sample_df.columns.tolist())
    print("数据形状:", sample_df.shape)
    print("数据集类型:", sample_df['set'].unique())
    
    # 检查是否有round_number列
    if 'round_number' in sample_df.columns:
        rounds = sample_df['round_number'].unique()
        print(f"\n轮数范围(样本): {min(rounds)} - {max(rounds)}")
        
        # 分组计算样本中每轮的平均准确率
        if 'accuracy' in sample_df.columns:
            sample_accuracy = sample_df.groupby(['round_number', 'set'])['accuracy'].mean().reset_index()
            test_acc = sample_accuracy[sample_accuracy['set'] == 'test']
            
            if not test_acc.empty:
                print("\n样本中测试集准确率:")
                print(test_acc[['round_number', 'accuracy']])
                
                max_acc_idx = test_acc['accuracy'].idxmax()
                max_round = test_acc.loc[max_acc_idx, 'round_number']
                max_acc = test_acc.loc[max_acc_idx, 'accuracy']
                print(f"\n样本中最高测试准确率: {max_acc:.4f} (轮数: {max_round})")
            
    # 现在尝试采样更多轮次的数据
    print("\n\n尝试按轮次采样更多数据...")
    
    # 读取文件的第一行获取列名
    headers = pd.read_csv(file_path, nrows=0).columns.tolist()
    
    # 使用更高效的方式读取数据 - 只读取我们需要的列
    required_cols = ['round_number', 'accuracy', 'loss', 'set']
    use_cols = [col for col in required_cols if col in headers]
    
    # 使用分块读取方式处理大文件
    chunks = []
    sample_rounds = set()
    max_rounds = 2000  # 假设最大轮数
    
    # 为了覆盖整个训练过程，我们选择一些关键点采样
    sample_points = [1] + list(range(50, max_rounds+1, 50))  # 第1轮和之后每50轮采样一次
    
    for chunk in pd.read_csv(file_path, usecols=use_cols, chunksize=10000):
        # 只保留我们想要的轮次
        filtered_chunk = chunk[chunk['round_number'].isin(sample_points)]
        if not filtered_chunk.empty:
            chunks.append(filtered_chunk)
            sample_rounds.update(filtered_chunk['round_number'].unique())
    
    if chunks:
        sampled_df = pd.concat(chunks)
        print(f"采样了 {len(sample_rounds)} 个轮次的数据")
        
        # 分析采样数据
        test_data = sampled_df[sampled_df['set'] == 'test']
        if not test_data.empty:
            test_by_round = test_data.groupby('round_number')['accuracy'].mean().reset_index()
            test_by_round = test_by_round.sort_values('round_number')
            
            print("\n各轮测试准确率:")
            print(test_by_round)
            
            max_acc_idx = test_by_round['accuracy'].idxmax()
            max_round = test_by_round.loc[max_acc_idx, 'round_number']
            max_acc = test_by_round.loc[max_acc_idx, 'accuracy']
            
            print(f"\n最高测试准确率: {max_acc:.4f} (轮数: {max_round})")
            
            # 简单的收敛判断 - 找到准确率接近最大值且后续变化小的点
            sorted_acc = test_by_round.sort_values('round_number')
            
            # 计算准确率的变化率
            sorted_acc['acc_change'] = sorted_acc['accuracy'].pct_change().abs()
            
            # 从最高准确率的50%处开始检查
            threshold = 0.95 * max_acc
            potential_convergence = sorted_acc[sorted_acc['accuracy'] >= threshold]
            
            if not potential_convergence.empty:
                convergence_idx = potential_convergence['acc_change'].idxmin()
                convergence_round = sorted_acc.loc[convergence_idx, 'round_number']
                convergence_acc = sorted_acc.loc[convergence_idx, 'accuracy']
                
                print(f"\n估计收敛轮数: {convergence_round}")
                print(f"收敛时准确率: {convergence_acc:.4f}")
                
                # 计算收敛后的平均准确率
                post_conv = sorted_acc[sorted_acc['round_number'] >= convergence_round]
                if not post_conv.empty:
                    avg_acc = post_conv['accuracy'].mean()
                    print(f"收敛后平均准确率: {avg_acc:.4f}")
    
except Exception as e:
    print(f"处理过程中出错: {str(e)}")
    import traceback
    traceback.print_exc() 