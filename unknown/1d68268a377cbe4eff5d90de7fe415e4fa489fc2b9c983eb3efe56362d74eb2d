import matplotlib.pyplot as plt
import numpy as np

# 数据来自之前的分析
rounds = [0, 100, 200, 300, 400, 500, 600, 700, 800, 900, 1000, 1500, 2000]
accuracy = [0.011, 0.227, 0.464, 0.537, 0.623, 0.667, 0.715, 0.743, 0.727, 0.744, 0.716, 0.700, 0.779]
loss = [4.124, 3.487, 2.075, 1.740, 1.375, 1.176, 0.949, 0.836, 0.902, 0.831, 0.939, 1.024, 0.679]

# 创建图形和子图
fig, (ax1, ax2) = plt.subplots(2, 1, figsize=(10, 12))
fig.suptitle('模型收敛分析', fontsize=16)

# 绘制准确率图
ax1.plot(rounds, accuracy, 'b-o', linewidth=2, markersize=8)
ax1.set_xlabel('轮数', fontsize=12)
ax1.set_ylabel('准确率', fontsize=12)
ax1.set_title('准确率随轮数的变化', fontsize=14)
ax1.grid(True)
ax1.set_xlim(0, 2100)
ax1.set_ylim(0, 1.0)

# 添加收敛点标记
ax1.axvline(x=700, color='r', linestyle='--', alpha=0.7, label='早期收敛点 (轮数=700)')
ax1.axhline(y=0.743, color='r', linestyle=':', alpha=0.7)
ax1.plot(700, 0.743, 'ro', markersize=10)
ax1.annotate(f'早期收敛点\n准确率: 0.743', xy=(700, 0.743), xytext=(500, 0.6),
            arrowprops=dict(facecolor='black', shrink=0.05, width=1.5, headwidth=8), fontsize=10)

ax1.axvline(x=2000, color='g', linestyle='--', alpha=0.7, label='最终收敛点 (轮数=2000)')
ax1.axhline(y=0.779, color='g', linestyle=':', alpha=0.7)
ax1.plot(2000, 0.779, 'go', markersize=10)
ax1.annotate(f'最终收敛点\n准确率: 0.779', xy=(2000, 0.779), xytext=(1700, 0.9),
            arrowprops=dict(facecolor='black', shrink=0.05, width=1.5, headwidth=8), fontsize=10)

# 标记三个阶段
ax1.annotate('快速收敛阶段', xy=(350, 0.4), fontsize=12, ha='center', bbox=dict(boxstyle="round,pad=0.3", fc="yellow", alpha=0.3))
ax1.annotate('波动阶段', xy=(1100, 0.72), fontsize=12, ha='center', bbox=dict(boxstyle="round,pad=0.3", fc="orange", alpha=0.3))
ax1.annotate('再优化阶段', xy=(1750, 0.74), fontsize=12, ha='center', bbox=dict(boxstyle="round,pad=0.3", fc="lightgreen", alpha=0.3))

ax1.legend(loc='lower right')

# 绘制损失图
ax2.plot(rounds, loss, 'r-o', linewidth=2, markersize=8)
ax2.set_xlabel('轮数', fontsize=12)
ax2.set_ylabel('损失', fontsize=12)
ax2.set_title('损失随轮数的变化', fontsize=14)
ax2.grid(True)
ax2.set_xlim(0, 2100)

# 添加收敛点标记
ax2.axvline(x=700, color='r', linestyle='--', alpha=0.7, label='早期收敛点 (轮数=700)')
ax2.axhline(y=0.836, color='r', linestyle=':', alpha=0.7)
ax2.plot(700, 0.836, 'ro', markersize=10)
ax2.annotate(f'早期收敛点\n损失: 0.836', xy=(700, 0.836), xytext=(500, 1.5),
            arrowprops=dict(facecolor='black', shrink=0.05, width=1.5, headwidth=8), fontsize=10)

ax2.axvline(x=2000, color='g', linestyle='--', alpha=0.7, label='最终收敛点 (轮数=2000)')
ax2.axhline(y=0.679, color='g', linestyle=':', alpha=0.7)
ax2.plot(2000, 0.679, 'go', markersize=10)
ax2.annotate(f'最终收敛点\n损失: 0.679', xy=(2000, 0.679), xytext=(1700, 0.3),
            arrowprops=dict(facecolor='black', shrink=0.05, width=1.5, headwidth=8), fontsize=10)

# 标记三个阶段
ax2.annotate('快速收敛阶段', xy=(350, 2.5), fontsize=12, ha='center', bbox=dict(boxstyle="round,pad=0.3", fc="yellow", alpha=0.3))
ax2.annotate('波动阶段', xy=(1100, 0.9), fontsize=12, ha='center', bbox=dict(boxstyle="round,pad=0.3", fc="orange", alpha=0.3))
ax2.annotate('再优化阶段', xy=(1750, 0.85), fontsize=12, ha='center', bbox=dict(boxstyle="round,pad=0.3", fc="lightgreen", alpha=0.3))

ax2.legend(loc='upper right')

# 调整布局并保存
plt.tight_layout(rect=[0, 0, 1, 0.96])
plt.savefig('convergence_analysis.png', dpi=300, bbox_inches='tight')
plt.close()

# 创建第二个图：准确率和损失在同一图中
plt.figure(figsize=(12, 8))
plt.title('准确率和损失随轮数的变化', fontsize=16)

# 创建两个Y轴
ax1 = plt.gca()
ax2 = ax1.twinx()

# 绘制准确率
line1, = ax1.plot(rounds, accuracy, 'b-o', linewidth=2, markersize=8, label='准确率')
ax1.set_xlabel('轮数', fontsize=14)
ax1.set_ylabel('准确率', color='b', fontsize=14)
ax1.tick_params(axis='y', labelcolor='b')
ax1.set_xlim(0, 2100)
ax1.set_ylim(0, 1.0)
ax1.grid(True, alpha=0.3)

# 绘制损失
line2, = ax2.plot(rounds, loss, 'r-^', linewidth=2, markersize=8, label='损失')
ax2.set_ylabel('损失', color='r', fontsize=14)
ax2.tick_params(axis='y', labelcolor='r')
ax2.set_ylim(0, 4.5)

# 添加收敛点标记
ax1.axvline(x=700, color='purple', linestyle='--', alpha=0.7, label='早期收敛点 (轮数=700)')
ax1.axvline(x=2000, color='green', linestyle='--', alpha=0.7, label='最终收敛点 (轮数=2000)')

# 标记三个阶段
plt.axvspan(0, 700, alpha=0.2, color='yellow', label='快速收敛阶段')
plt.axvspan(700, 1500, alpha=0.2, color='orange', label='波动阶段')
plt.axvspan(1500, 2000, alpha=0.2, color='lightgreen', label='再优化阶段')

# 合并图例
lines = [line1, line2]
labels = [l.get_label() for l in lines]
ax1.legend(lines, labels, loc='center right', fontsize=12)

# 添加网格线
plt.grid(True, alpha=0.3)

# 保存图表
plt.tight_layout()
plt.savefig('convergence_combined.png', dpi=300, bbox_inches='tight')
plt.close()

print("图表已生成: convergence_analysis.png 和 convergence_combined.png")
