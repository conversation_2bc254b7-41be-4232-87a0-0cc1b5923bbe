# Synthetic Dataset

We propose a process to generate synthetic federated datasets. The dataset is inspired by the one presented by [<PERSON> et al.](https://arxiv.org/abs/1905.10497), but has possible additional heterogeneity designed to make current meta-learning methods (such as [Reptile](https://openai.com/blog/reptile/)) struggle. The high-level goal is to create tasks whose true models are (1) task-dependant, and (2) clustered around more than just one center. To see a description of the whole generative process, please refer to the LEAF paper.

We note that, at the moment, we default to one cluster of models in our code. This can be easily changed by modifying the PROB_CLUSTERS constant in ```main.py```.

## Setup Instructions
- pip3 install numpy
- pip3 install pillow
- Run ```python main.py -num-tasks 1000 -num-classes 5 -num-dim 60``` to generate the initial data.
- Run the ```./preprocess.sh``` (as with the other LEAF datasets) to produce the final data splits. We suggest using the following tags:
    - ```--sf``` := fraction of data to sample, written as a decimal; set it to 1.0 in order to keep the number of tasks/users specified earlier.
    - ```-k``` := minimum number of samples per user; set it to 5.
    - ```-t``` := 'user' to partition users into train-test groups, or 'sample' to partition each user's samples into train-test groups.
    - ```--tf``` := fraction of data in training set, written as a decimal; default is 0.9.
    - ```--smplseed``` := seed to be used before random sampling of data.
    - ```--spltseed``` :=  seed to be used before random split of data.

i.e.
- ```./preprocess.sh -s niid --sf 1.0 -k 5 -t sample --tf 0.6```

Make sure to delete the rem_user_data, sampled_data, test, and train subfolders in the data directory before re-running preprocess.sh

## Notes
- More details on ```preprocess.sh```:
  - The order in which ```preprocess.sh``` processes data is 1. generating all_data (done here by the ```main.py``` script), 2. sampling, 3. removing users, and 4. creating train-test split. The script will look at the data in the last generated directory and continue preprocessing from that point. For example, if the ```all_data``` directory has already been generated and the user decides to skip sampling and only remove users with the ```-k``` tag (i.e. running ```preprocess.sh -k 50```), the script will effectively apply a remove user filter to data in ```all_data``` and place the resulting data in the ```rem_user_data``` directory.
  - File names provide information about the preprocessing steps taken to generate them. For example, the ```all_data_niid_1_keep_64.json``` file was generated by first sampling 10 percent (.1) of the data ```all_data.json``` in a non-i.i.d. manner and then applying the ```-k 64``` argument to the resulting data.
- Each .json file is an object with 3 keys:
  1. 'users', a list of users
  2. 'num_samples', a list of the number of samples for each user, and 
  3. 'user_data', an object with user names as keys and their respective data as values.
- Run ```./stats.sh``` to get statistics of data (data/all_data/all_data.json must have been generated already)
- In order to run reference implementations in ```../models``` directory, the ```-t sample``` tag must be used when running ```./preprocess.sh```
