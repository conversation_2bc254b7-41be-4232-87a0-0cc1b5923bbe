Installation and Contribution
=============================

Dependencies
-----------

LEAF has been used under Python 3.5, and is designed to work with the TensorFlow
and NumPy ecosystem. The dependency requirements are:

* scipy(>=0.13.3)
* tensorflow(>=1.10) (or tensorflow-gpu)
* numpy

Additionally, you might need:

* `wget` (for fetching data, if not already present)
* matplotlib(>=2.0.0)

Installation
------------

- Clone the repo from GitHub: https://github.com/TalwalkarLab/leaf
- Install the libraries listed in ```requirements.txt```
    - ```pip3 install -r requirements.txt```
- Go to directory of respective dataset for instructions on fetching and generating data
- ```models``` directory contains instructions on running baseline reference implementations

Contribute
----------

You can contribute to this framework by submitting pull requests to the GitHub repo

GitHub: https://github.com/TalwalkarLab/leaf/pulls
