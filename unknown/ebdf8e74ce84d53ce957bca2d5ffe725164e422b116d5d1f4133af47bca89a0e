html {
	background-color: white;
}

body {
	font:14px/1.5 Helvetica;
	padding:0;
	margin:0;
	color: #282828;
}
.background{
	background-color: #E8E8E8;
	padding-top: 10px;
}
.toptitle {
	text-align: center;
    margin-top: 50px;
    background-size: cover;
    padding-left: 70px;
    padding-right: 70px;
    font-family: 'Roboto', sans-serif;
}
.toptitle h1 {
	font: normal 48px/1.5 sans-serif;
	line-height: 0px;
	font-family: 'Roboto', sans-serif;
}
.toptitle h2 {
	margin-top: -20px;
	margin-bottom: 0px;
	font: normal 24px/1.5 sans-serif;
	font-family: '<PERSON>o', sans-serif;
}
.title {
	text-align: center;
    margin-top: 50px;
    background-size: cover;
    padding-left: 100px;
    padding-right: 100px;
    font-family: 'Roboto', sans-serif;
}
.title h1 {
	font: normal 48px/1.5 sans-serif;
	line-height: 0px;
	font-family: 'Roboto', sans-serif;
}
.title h2 {
	font: normal 24px/1.5 sans-serif;
	font-family: '<PERSON><PERSON>', sans-serif;
}
.title img {
	opacity: 0.8;
	margin: 20px auto;
}

span.subtitle {
    text-align: center;
	font: normal 24px/1.5 sans-serif;
	color: #00a571;
	font-family: 'Roboto', sans-serif;
}

span.description {
	text-align: center;
	font: normal 18px/1.5 Helvetica;
	width: 85%;
	margin: 0 auto;
    padding: 10px;
    font-family: 'Roboto', sans-serif;
}

span.description a {
	color: #376a31;
	text-decoration: none;
}

span.citation {
	text-align: left;
	font: normal 16px/1.5 Helvetica;
	width: 85%;
	margin: 0 auto;
    padding: 10px;
    font-family: 'Roboto', sans-serif;
}

.title h3{
	text-align: center;
	color: #376a31;
	font-family: 'Roboto', sans-serif;
}
.title h3 a{
	color:inherit;
	text-decoration: none;
	display: inline-block;
	border: 1px solid #196619;
	padding: 10px 15px;
	border-radius: 3px;
	font: bold 16px/1 'Open Sans', sans-serif;
	text-transform: uppercase;
	font-family: 'Roboto', sans-serif;
}
.title h3 a:hover{
	background-color:#196619;
	transition:0.2s;
	color:#fff;
}

.overview{
	width: 700px;
	margin:0 auto;
	text-align: center;
}

.showHide { display:none; }
@media screen and (min-width: 500px) {
	.showHide  { display: inline; }
}

.menu{
	max-width: 900px;
	text-align: center;
	margin: 0px auto;
	font-family: 'Roboto', sans-serif;
}

.vertspace {
	padding-top: 20px;
	padding-bottom: 20px;
}

.infobox {
	display: -webkit-box;
	display: -moz-box;
	display: -ms-flexbox;
	display: -webkit-flex;
	display: flex;

	-webkit-flex-flow: row wrap;
	flex-flow: row wrap;
	justify-content: space-around;

	width: 85%;
	margin-left: auto;
	margin-right: auto;
	background: white;
	font-family: 'Roboto', sans-serif;
}

.infobox-1 {
	padding: 5px 0;
	background: white;
	vertical-align: middle;
	font-family: 'Roboto', sans-serif;
}
.infobox-2 {
	padding: 5px 0;
	background: white;
	font-family: 'Roboto', sans-serif;
}

.collapsible {
	background-color: #E8E8E8;
	font: normal 16px Helvetica;
    color: #282828;
    cursor: pointer;
    width: 100%;
    border: none;
	text-align: left;
    outline: none;
	font-size: 15px;
	border-bottom: 3px solid white;
	font-family: 'Roboto', sans-serif;
}

.collapsible font{
	padding: 10px;
	font-family: 'Roboto', sans-serif;
}

.collapsible img{
	padding: 5px;
}

.active, .collapsible:hover {
    background-color: #196619;
    color: white;
}

.content {
	background-color: #f1f1f1;
	border-bottom: 1px solid white;
	max-height: 0;
    overflow: hidden;
    transition: max-height 0.2s ease-out;
}