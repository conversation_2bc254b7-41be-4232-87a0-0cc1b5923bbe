#!/usr/bin/env python
"""
FEMNIST Enhanced Model Experiment Script

This script demonstrates how to use the enhanced CNN model for FEMNIST
and run experiments with different hyperparameters to compare performance.
"""

import argparse
import importlib
import os
import sys
import csv
import datetime
import numpy as np
import tensorflow as tf

# Make sure the current directory is in the path
sys.path.append(os.path.dirname(os.path.abspath(__file__)))

from utils.args import parse_args
from utils.model_utils import read_data
from baseline_constants import MAIN_PARAMS, MODEL_PARAMS
from client import Client
from server import Server

# Default experiment configurations
DEFAULT_CONFIGS = {
    "original": {
        "model_path": "femnist.cnn",
        "optimizer": "sgd",
        "dropout": 0.0,
        "batch_norm": False,
        "lr": 0.06,
        "minibatch": 0.1, 
        "clients_per_round": 3,
        "num_rounds": 100,
        "num_epochs": 1,
        "output_dir": "metrics/original"
    },
    "enhanced_sgd": {
        "model_path": "femnist.enhanced_cnn",
        "optimizer": "sgd",
        "dropout": 0.5,
        "batch_norm": True,
        "lr": 0.06,
        "minibatch": 0.1,
        "clients_per_round": 3,
        "num_rounds": 100, 
        "num_epochs": 1,
        "output_dir": "metrics/enhanced_sgd"
    },
    "enhanced_adam": {
        "model_path": "femnist.enhanced_cnn",
        "optimizer": "adam",
        "dropout": 0.5,
        "batch_norm": True,
        "lr": 0.001,  # Lower learning rate for Adam
        "minibatch": 0.1,
        "clients_per_round": 3,
        "num_rounds": 100,
        "num_epochs": 1,
        "output_dir": "metrics/enhanced_adam"
    },
    "enhanced_fedavg": {
        "model_path": "femnist.enhanced_cnn",
        "optimizer": "sgd",
        "dropout": 0.5,
        "batch_norm": True,
        "lr": 0.004,  # Lower learning rate for FedAvg
        "minibatch": None,  # No minibatch for FedAvg
        "clients_per_round": 3,
        "num_rounds": 100,
        "num_epochs": 5,  # Multiple local epochs for FedAvg
        "output_dir": "metrics/enhanced_fedavg"
    }
}

def create_client_model(args, config, num_classes=62):
    """Create a client model based on configuration."""
    # Import the model module
    mod = importlib.import_module(config["model_path"])
    ClientModel = getattr(mod, 'ClientModel')
    
    # Create client model
    if config["model_path"] == "femnist.cnn":
        # Original model only takes basic parameters
        client_model = ClientModel(args.seed, config["lr"], num_classes)
    else:
        # Enhanced model takes additional parameters
        client_model = ClientModel(
            args.seed, 
            config["lr"], 
            num_classes,
            optimizer_type=config["optimizer"],
            dropout_rate=config["dropout"],
            use_batch_norm=config["batch_norm"]
        )
    
    return client_model

def setup_clients(dataset, model=None, use_val_set=False):
    """Instantiates clients based on given train and test data directories."""
    eval_set = 'test' if not use_val_set else 'val'
    train_data_dir = os.path.join('..', 'data', dataset, 'data', 'train')
    test_data_dir = os.path.join('..', 'data', dataset, 'data', eval_set)

    users, groups, train_data, test_data = read_data(train_data_dir, test_data_dir)

    if len(groups) == 0:
        groups = [[] for _ in users]
    
    clients = [Client(u, g, train_data[u], test_data[u], model) for u, g in zip(users, groups)]
    return clients

def print_metrics(metrics, weights, prefix=''):
    """Prints weighted averages of the given metrics."""
    ordered_weights = [weights[c] for c in sorted(weights)]
    ordered_metrics = {metric: [metrics[c][metric] for c in sorted(metrics)] 
                      for metric in metrics[sorted(metrics)[0]]}
    
    results = {}
    for metric in ordered_metrics:
        avg = np.average(ordered_metrics[metric], weights=ordered_weights)
        p10 = np.percentile(ordered_metrics[metric], 10)
        p50 = np.percentile(ordered_metrics[metric], 50)
        p90 = np.percentile(ordered_metrics[metric], 90)
        
        print('%s: %g, 10th perc: %g, 50th perc: %g, 90th perc: %g' % 
              (prefix + metric, avg, p10, p50, p90))
        
        results[prefix + metric] = avg
    
    return results

def save_results_to_csv(results_dir, experiment_name, round_num, results):
    """Save results to CSV file."""
    os.makedirs(results_dir, exist_ok=True)
    
    csv_path = os.path.join(results_dir, f"{experiment_name}_results.csv")
    file_exists = os.path.isfile(csv_path)
    
    with open(csv_path, mode='a', newline='') as file:
        fieldnames = ['round'] + list(results.keys())
        writer = csv.DictWriter(file, fieldnames=fieldnames)
        
        if not file_exists:
            writer.writeheader()
        
        row_data = {'round': round_num}
        row_data.update(results)
        writer.writerow(row_data)

def run_experiment(args, config_name):
    """Run an experiment with the specified configuration."""
    print(f"\n{'='*80}\nRunning experiment: {config_name}\n{'='*80}")
    
    # Get configuration
    config = DEFAULT_CONFIGS[config_name]
    
    # Create output directory
    os.makedirs(config["output_dir"], exist_ok=True)
    
    # Set random seeds for reproducibility
    tf.set_random_seed(args.seed)
    np.random.seed(args.seed)
    
    # Reset TensorFlow graph
    tf.reset_default_graph()
    
    # Create client model
    client_model = create_client_model(args, config)
    
    # Create server
    server = Server(client_model)
    
    # Create clients
    clients = setup_clients(args.dataset, client_model)
    client_ids, client_groups, client_num_samples = server.get_clients_info(clients)
    print(f'Clients in Total: {len(clients)}')
    
    # Results dictionary to track metrics
    all_results = {}
    
    # Training loop
    for i in range(config["num_rounds"]):
        print(f'--- Round {i+1} of {config["num_rounds"]}: Training {config["clients_per_round"]} Clients ---')
        
        # Select clients to train this round
        server.select_clients(i, clients, num_clients=config["clients_per_round"])
        c_ids, c_groups, c_num_samples = server.get_clients_info(server.selected_clients)
        
        # Train model
        if config["minibatch"] is not None:
            # Use minibatch SGD
            sys_metrics = server.train_model(
                num_epochs=config["num_epochs"], 
                batch_size=args.batch_size, 
                minibatch=config["minibatch"]
            )
        else:
            # Use FedAvg (no minibatch)
            sys_metrics = server.train_model(
                num_epochs=config["num_epochs"], 
                batch_size=args.batch_size
            )
        
        # Update server model
        server.update_model()
        
        # Test and report results every few rounds
        if (i + 1) % args.eval_every == 0 or (i + 1) == config["num_rounds"]:
            # Test on training set
            train_metrics = server.test_model(clients, set_to_use='train')
            train_results = print_metrics(train_metrics, client_num_samples, prefix='train_')
            
            # Test on test set
            test_metrics = server.test_model(clients, set_to_use='test')
            test_results = print_metrics(test_metrics, client_num_samples, prefix='test_')
            
            # Combine results and save to CSV
            results = {**train_results, **test_results}
            save_results_to_csv(config["output_dir"], config_name, i + 1, results)
            all_results[i+1] = results
    
    # Save model checkpoint
    ckpt_path = os.path.join('checkpoints', args.dataset, config_name)
    os.makedirs(ckpt_path, exist_ok=True)
    save_path = server.save_model(os.path.join(ckpt_path, 'model.ckpt'))
    print(f'Model saved to: {save_path}')
    
    # Close model to free resources
    server.close_model()
    
    return all_results

def main():
    """Main function to run the experiments."""
    # Parse command line arguments
    parser = argparse.ArgumentParser()
    
    parser.add_argument('--dataset', type=str, default='femnist', 
                        help='name of dataset')
    parser.add_argument('--model', type=str, default='cnn', 
                        help='model name (original or enhanced)')
    parser.add_argument('--experiments', type=str, nargs='+', 
                        default=['original', 'enhanced_sgd', 'enhanced_adam', 'enhanced_fedavg'],
                        help='experiments to run')
    parser.add_argument('--batch_size', type=int, default=10,
                        help='batch size when clients train on data')
    parser.add_argument('--seed', type=int, default=42,
                        help='random seed for reproducibility')
    parser.add_argument('--eval_every', type=int, default=5,
                        help='evaluate every n rounds')
    
    args = parser.parse_args()
    
    # Check if data exists
    data_path = os.path.join('..', 'data', args.dataset, 'data', 'train')
    if not os.path.exists(data_path):
        print(f"Error: Data not found at {data_path}")
        print("Please run the preprocessing script first to download and prepare the data.")
        print("For example: cd ../data/femnist && ./preprocess.sh -s niid --sf 0.05 -k 0 -t sample")
        sys.exit(1)
    
    # Store results for all experiments
    all_experiment_results = {}
    
    # Run all specified experiments
    for experiment in args.experiments:
        if experiment not in DEFAULT_CONFIGS:
            print(f"Warning: Unknown experiment {experiment}. Skipping...")
            continue
        
        experiment_results = run_experiment(args, experiment)
        all_experiment_results[experiment] = experiment_results
    
    # Print final comparison summary
    print("\n" + "="*30 + " FINAL RESULTS SUMMARY " + "="*30)
    for experiment, results in all_experiment_results.items():
        final_round = max(results.keys())
        test_acc = results[final_round].get('test_accuracy', 0.0)
        print(f"{experiment:15s}: Final test accuracy = {test_acc:.4f}")
    
    print("\nExperiments completed successfully!")

if __name__ == "__main__":
    main()
