import os
import docx
import requests
import time
from docx import Document
from docx.shared import Pt
from docx.enum.text import WD_PARAGRAPH_ALIGNMENT

# DeepL API configuration
# You'll need to replace this with your own API key
DEEPL_API_KEY = "YOUR_DEEPL_API_KEY"
DEEPL_API_URL = "https://api-free.deepl.com/v2/translate"  # Use api.deepl.com for Pro account

def configure_deepl():
    """Ask for DeepL API key if not set"""
    global DEEPL_API_KEY
    if DEEPL_API_KEY == "YOUR_DEEPL_API_KEY":
        print("DeepL API key is required.")
        DEEPL_API_KEY = input("Enter your DeepL API key: ")

def translate_text_deepl(text, target_lang='ZH'):
    """Translate text using DeepL API"""
    if not text.strip():
        return ""
    
    # Build request parameters
    params = {
        'auth_key': DEEPL_API_KEY,
        'text': text,
        'target_lang': target_lang
    }
    
    # Make API request
    try:
        response = requests.post(DEEPL_API_URL, data=params)
        response.raise_for_status()  # Raise exception for error status codes
        result = response.json()
        
        if 'translations' in result and result['translations']:
            return result['translations'][0]['text']
        return text
    except Exception as e:
        print(f"Translation error: {e}")
        return text

def translate_text_local(text, to_lang='ZH'):
    """Local translation fallback using pre-defined dictionary (very limited)"""
    # This is a very basic offline fallback with just a few common translations
    translations = {
        "Name": "姓名",
        "Address": "地址",
        "Education": "教育",
        "Experience": "经验",
        "Skills": "技能",
        "Contact": "联系方式",
        "Email": "电子邮件",
        "Phone": "电话",
        "University": "大学",
        "Resume": "简历",
        "Curriculum Vitae": "个人简历",
        "Projects": "项目",
        "Work Experience": "工作经验",
        "Languages": "语言",
        "Certifications": "证书",
        "References": "推荐人",
        "Date of Birth": "出生日期"
    }
    
    # Check if the text is in our basic dictionary
    if text in translations:
        return translations[text]
        
    # If not in dictionary, return with a prefix to indicate it's untranslated
    return f"[需翻译] {text}"

def split_into_chunks(text, max_length=1000):
    """Split text into chunks for API limits"""
    if len(text) <= max_length:
        return [text]
    
    chunks = []
    # Try to split at sentence boundaries
    sentences = text.split('. ')
    current_chunk = ""
    
    for sentence in sentences:
        # Add period back if it was removed during split
        if sentence != sentences[-1]:
            sentence += '. '
            
        if len(current_chunk) + len(sentence) <= max_length:
            current_chunk += sentence
        else:
            # Save the current chunk and start a new one
            chunks.append(current_chunk)
            current_chunk = sentence
    
    # Add the last chunk if it contains text
    if current_chunk:
        chunks.append(current_chunk)
        
    return chunks

def preserve_run_properties(original_run, new_run):
    """Copy all formatting properties from original run to new run"""
    # Copy basic properties
    new_run.bold = original_run.bold
    new_run.italic = original_run.italic
    new_run.underline = original_run.underline
    
    # Copy font properties
    if original_run.font.name:
        new_run.font.name = original_run.font.name
    if original_run.font.size:
        new_run.font.size = original_run.font.size
    if original_run.font.color.rgb:
        new_run.font.color.rgb = original_run.font.color.rgb
        
    return new_run

def translate_paragraph(paragraph, use_local=False):
    """Translate a paragraph while preserving its formatting"""
    if not paragraph.text.strip():
        return  # Skip empty paragraphs
    
    # Save original paragraph properties
    alignment = paragraph.alignment
    style = paragraph.style
    
    # Store original runs with formatting
    original_runs = []
    for run in paragraph.runs:
        original_runs.append({
            'text': run.text,
            'obj': run
        })
    
    # Get paragraph text
    full_text = paragraph.text
    
    # Translate the text
    translated_text = ""
    if use_local:
        # Use local translation
        translated_text = translate_text_local(full_text)
    else:
        # Use DeepL with chunking for long paragraphs
        chunks = split_into_chunks(full_text)
        translated_chunks = []
        
        for chunk in chunks:
            translated_chunk = translate_text_deepl(chunk)
            translated_chunks.append(translated_chunk)
            time.sleep(0.5)  # Rate limiting
            
        translated_text = ''.join(translated_chunks)
    
    # Clear the paragraph
    p = paragraph._p
    for _ in range(len(paragraph.runs)):
        if len(p) > 0:
            p.remove(p[0])
    
    # Add translated text
    if original_runs:
        new_run = paragraph.add_run(translated_text)
        preserve_run_properties(original_runs[0]['obj'], new_run)
    else:
        paragraph.add_run(translated_text)
    
    # Restore paragraph properties
    paragraph.alignment = alignment
    paragraph.style = style

def translate_table(table, use_local=False):
    """Translate all text in a table"""
    for row in table.rows:
        for cell in row.cells:
            for paragraph in cell.paragraphs:
                translate_paragraph(paragraph, use_local)

def translate_document(input_path, output_path, use_local=False):
    """Translate the entire document while preserving formatting"""
    try:
        doc = Document(input_path)
        
        # Translate paragraphs
        print("Translating paragraphs...")
        for i, paragraph in enumerate(doc.paragraphs):
            if i % 5 == 0:
                print(f"Processing paragraph {i+1}/{len(doc.paragraphs)}")
            translate_paragraph(paragraph, use_local)
        
        # Translate tables
        print("Translating tables...")
        for i, table in enumerate(doc.tables):
            print(f"Processing table {i+1}/{len(doc.tables)}")
            translate_table(table, use_local)
        
        # Save the translated document
        print(f"Saving translated document to {output_path}")
        doc.save(output_path)
        return True
    except Exception as e:
        print(f"Error in document translation: {e}")
        import traceback
        traceback.print_exc()
        return False

def main():
    input_file = r"C:\Users\<USER>\Desktop\private-ai\leaf\CV.docx"
    output_file = r"C:\Users\<USER>\Desktop\private-ai\leaf\CV_Chinese_DeepL.docx"
    
    print(f"Starting translation of {input_file}")
    
    # Determine whether to use local translation or DeepL API
    use_local = True  # Change to False to use DeepL API
    
    if not use_local:
        configure_deepl()
    else:
        print("Running in LOCAL mode with limited translation dictionary")
        print("Set use_local = False in the code to use the DeepL API")
    
    success = translate_document(input_file, output_file, use_local)
    
    if success:
        print("Translation complete!")
        print(f"Translated document saved to: {output_file}")
    else:
        print("Translation failed. Check the error messages above.")

if __name__ == "__main__":
    main() 