.. leaf documentation master file, created by
   sphinx-quickstart on Mon Feb 11 03:11:19 2019.
   You can adapt this file completely to your liking, but it should at least
   contain the root `toctree` directive.


################################
Welcome to LEAF's documentation!
################################

LEAF is a benchmarking framework for learning in federated settings, with applications including federated learning, multi-task learning, meta-learning, and on-device learning.

See the source code repository on GitHub: https://github.com/TalwalkarLab/leaf

.. toctree::
   :maxdepth: 2
   :hidden:
   :caption: Getting Started

   install/get_leaf

.. toctree::
   :maxdepth: 2
   :hidden:
   :caption: User Guide

   tutorials/index

.. toctree::
   :maxdepth: 2
   :hidden:
   :caption: API Documentation

   autodoc/modules

.. toctree::
   :maxdepth: 2
   :hidden:
   :caption: Additional Information

   contact
   citations

`Getting started <install/get_leaf.html>`_
------------------------------------------

Information to install, test, and contribute to the package.

`User Guide <tutorials/index.html>`_
------------------------------------

A set of examples illustrating the use of different models and datasets with LEAF.

`API Documentation <autodoc/modules.html>`_
-------------------------------------------

The exact API of all functions and classes, as given in the
docstring. 

`Contact <contact.html>`_
-------------------------

Contact information of LEAF authors

Indices and tables
==================

* :ref:`genindex`
* :ref:`modindex`
* :ref:`search`

