import os
import docx
import time
import torch
from docx import Document
from docx.shared import Pt
from transformers import MarianMTModel, MarianTokenizer

# Constants
MODEL_NAME = "Helsinki-NLP/opus-mt-en-zh"  # English to Chinese

def load_translation_model(model_name=MODEL_NAME):
    """Load the translation model and tokenizer"""
    print(f"Loading translation model: {model_name}")
    print("This may take a while on first run as the model is downloaded...")
    
    try:
        tokenizer = MarianTokenizer.from_pretrained(model_name)
        model = MarianMTModel.from_pretrained(model_name)
        
        # Check if CUDA is available and move model to GPU
        if torch.cuda.is_available():
            print("CUDA is available. Using GPU for translation.")
            model = model.to("cuda")
        else:
            print("CUDA not available. Using CPU for translation.")
        
        return model, tokenizer
    except Exception as e:
        print(f"Error loading model: {e}")
        return None, None

def translate_text_huggingface(text, model, tokenizer):
    """Translate text using the Hugging Face model"""
    if not text.strip():
        return ""
    
    try:
        # Avoid too long sequences
        if len(text) > 500:
            # Split text into chunks and translate separately
            chunks = split_into_chunks(text, 500)
            results = []
            for chunk in chunks:
                translated = translate_chunk(chunk, model, tokenizer)
                results.append(translated)
            return " ".join(results)
        else:
            return translate_chunk(text, model, tokenizer)
    except Exception as e:
        print(f"Translation error: {e}")
        return text

def translate_chunk(text, model, tokenizer):
    """Translate a single chunk of text"""
    # Tokenize
    batch = tokenizer([text], return_tensors="pt", padding=True, truncation=True, max_length=512)
    
    # Move to GPU if available
    if torch.cuda.is_available():
        batch = {k: v.to("cuda") for k, v in batch.items()}
    
    # Generate translation
    gen = model.generate(**batch)
    
    # Decode
    translated = tokenizer.batch_decode(gen, skip_special_tokens=True)
    
    return translated[0] if translated else ""

def split_into_chunks(text, max_length=500):
    """Split text into chunks for processing"""
    if len(text) <= max_length:
        return [text]
    
    # Try to split at sentence boundaries
    sentences = text.split('. ')
    
    chunks = []
    current_chunk = ""
    
    for sentence in sentences:
        # Add period back if it was removed during split
        if sentence != sentences[-1]:
            sentence += '. '
            
        if len(current_chunk) + len(sentence) <= max_length:
            current_chunk += sentence
        else:
            # Save the current chunk and start a new one
            chunks.append(current_chunk)
            current_chunk = sentence
    
    # Add the last chunk if it contains text
    if current_chunk:
        chunks.append(current_chunk)
        
    return chunks

def preserve_run_properties(original_run, new_run):
    """Copy all formatting properties from original run to new run"""
    # Copy basic properties
    new_run.bold = original_run.bold
    new_run.italic = original_run.italic
    new_run.underline = original_run.underline
    
    # Copy font properties
    if original_run.font.name:
        new_run.font.name = original_run.font.name
    if original_run.font.size:
        new_run.font.size = original_run.font.size
    if original_run.font.color.rgb:
        new_run.font.color.rgb = original_run.font.color.rgb
        
    return new_run

def translate_paragraph(paragraph, model, tokenizer):
    """Translate a paragraph while preserving its formatting"""
    if not paragraph.text.strip():
        return  # Skip empty paragraphs
    
    # Store original paragraph properties
    alignment = paragraph.alignment
    style = paragraph.style
    
    # Store original run information with formatting
    original_runs = []
    for run in paragraph.runs:
        original_runs.append({
            'text': run.text,
            'obj': run
        })
    
    # Get paragraph text
    full_text = paragraph.text
    
    # Translate the text
    translated_text = translate_text_huggingface(full_text, model, tokenizer)
    
    # Clear the paragraph
    p = paragraph._p
    for _ in range(len(paragraph.runs)):
        if len(p) > 0:  # Check if there are still runs to remove
            p.remove(p[0])
    
    # Add the translated text with formatting from the first run
    if original_runs:
        new_run = paragraph.add_run(translated_text)
        preserve_run_properties(original_runs[0]['obj'], new_run)
    else:
        paragraph.add_run(translated_text)
    
    # Restore paragraph properties
    paragraph.alignment = alignment
    paragraph.style = style

def translate_table(table, model, tokenizer):
    """Translate all cells in a table"""
    for row in table.rows:
        for cell in row.cells:
            for paragraph in cell.paragraphs:
                translate_paragraph(paragraph, model, tokenizer)

def translate_document(input_path, output_path, model, tokenizer):
    """Translate the entire document while preserving formatting"""
    try:
        doc = Document(input_path)
        
        # Check if document loaded successfully
        if not doc:
            print(f"Failed to load document from {input_path}")
            return False
        
        # Translate each paragraph
        print("Translating paragraphs...")
        for i, paragraph in enumerate(doc.paragraphs):
            if i % 5 == 0:
                print(f"Processing paragraph {i+1}/{len(doc.paragraphs)}")
            translate_paragraph(paragraph, model, tokenizer)
        
        # Translate tables
        print("Translating tables...")
        for i, table in enumerate(doc.tables):
            print(f"Processing table {i+1}/{len(doc.tables)}")
            translate_table(table, model, tokenizer)
        
        # Save the translated document
        print(f"Saving translated document to {output_path}")
        doc.save(output_path)
        return True
    except Exception as e:
        print(f"Error in document translation: {e}")
        import traceback
        traceback.print_exc()
        return False

def main():
    input_file = r"C:\Users\<USER>\Desktop\private-ai\leaf\CV.docx"
    output_file = r"C:\Users\<USER>\Desktop\private-ai\leaf\CV_Chinese_HuggingFace.docx"
    
    print(f"Starting translation of {input_file}")
    
    # Load translation model and tokenizer
    model, tokenizer = load_translation_model()
    
    if model is None or tokenizer is None:
        print("Failed to load translation model. Exiting.")
        return
    
    print("Model loaded successfully. Starting translation...")
    
    success = translate_document(input_file, output_file, model, tokenizer)
    
    if success:
        print("Translation complete!")
        print(f"Translated document saved to: {output_file}")
    else:
        print("Translation failed. Check the error messages above.")

if __name__ == "__main__":
    main() 